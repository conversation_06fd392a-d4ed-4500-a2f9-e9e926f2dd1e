
# Subject configuration - UPDATED FOR NEXT TIME FRAME
SUBJECT_ID = "male_169"
SUBJECT_TYPE = "male"
SUBJECT_NUMBER = "169"
TIME_FRAME = 5  # 新增：指定时间帧
SUBFOLDER = 'cine'

# Base paths
BASE_DATA_PATH = "/home/<USER>/XCAT_Project/output"
BASE_OUTPUT_PATH = "/home/<USER>/XCAT_Project/outputData"

# Generate paths
data_folder = BASE_DATA_PATH
subject_folder = f"{data_folder}/{SUBJECT_ID}/{SUBFOLDER}"

# 为不同时间帧创建独立的输出文件夹
results_folder = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{TIME_FRAME:02d}"
output_folder_bin = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{TIME_FRAME:02d}/bin_output"
warped_images = f"{BASE_OUTPUT_PATH}/{SUBJECT_ID}/{SUBFOLDER}/frame_{TIME_FRAME:02d}/warped_images"

# 更新输入文件路径为下一个时间帧
file_input = f"{subject_folder}/{SUBJECT_ID}_cine.samp_act_{TIME_FRAME}.vti"

print(f"Configuration for Time Frame {TIME_FRAME}:")
print(f"  Subject ID: {SUBJECT_ID}")
print(f"  Input file: {file_input}")
print(f"  Output folder: {results_folder}")

# 检查文件是否存在
import os
if not os.path.exists(file_input):
    print(f"❌ Error: File not found at {file_input}")
    # 列出可用的文件
    available_files = [f for f in os.listdir(subject_folder) if f.endswith('.vti')]
    print(f"Available VTI files: {available_files}")
else:
    print(f"✅ Input file found!")
    
# Create directories if they don't exist
for folder in [results_folder, output_folder_bin, warped_images]:
    if not os.path.exists(folder):
        os.makedirs(folder)

if not os.path.exists(results_folder + "/SAX_slices"):
    os.makedirs(results_folder + "/SAX_slices")

if not os.path.exists(results_folder + "/Tissue_properties_initial_frame"):
    os.makedirs(results_folder + "/Tissue_properties_initial_frame")


    
import vtk
from vtk.util.numpy_support import vtk_to_numpy

reader = vtk.vtkXMLImageDataReader()
reader.SetFileName(file_input)
reader.Update()

vtiIMG = reader.GetOutput()
print(f"Loaded time frame {TIME_FRAME} successfully!")
print(f"Image dimensions: {vtiIMG.GetDimensions()}")

# 继续复制你的其他处理代码...
# Extract label data from VTI file
reader = vtk.vtkXMLImageDataReader()
reader.SetFileName(file_input)
reader.Update()
img_ref = reader.GetOutput()

origin = img_ref.GetOrigin()
original_dims = img_ref.GetDimensions()  # [950, 256, 256]
original_spacing = img_ref.GetSpacing()

# Swap dimensions so Z is the last dimension
dim = [original_dims[1], original_dims[2], original_dims[0]]  # [256, 256, 950]
spacing = [original_spacing[1], original_spacing[2], original_spacing[0]]
    
print("Model info:")
print(f"Origin: {origin}")
print(f"Dimensions: {dim}")
print(f"Spacing: {spacing}")


# 从这里开始，复制你原来 notebook 的所有处理代码
# 只需要把 file_input, results_folder 等路径变量用上面新定义的即可
import os

import numpy as np
# Workaround for NumPy/VTK compatibility issue
# Fix for "numpy has no attribute 'bool'" error with newer NumPy versions
# if not hasattr(np, 'bool'):
#     np.bool = bool
# if not hasattr(np, 'int'):
#     np.int = int
# if not hasattr(np, 'float'):
#     np.float = float

import vtk
import matplotlib.pyplot as plt
from vtkmodules.util import numpy_support
from vtk.util.numpy_support import vtk_to_numpy
from PIL import Image
import sys
import scipy.ndimage as ndimage
from matplotlib import cm
from vtk.util.numpy_support import numpy_to_vtk
# Import local modules
sys.path.insert(0, './myWarpFunctions')
sys.path.insert(0, './myTexturizer')
import myMorphingFunctions as MMF
from texturizationFunctions import defineTissueProperties, defineTissuePropertiesMRXCAT, fixLVTexture, textureReport, XCATProperties
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
import time

# Define run flags
runWarper = True  # Run the slice warping process
runTexturizer = True  # Run the texturization process
runConverter = True  # Convert VTI files to binary
process_all_slices = True  # Process all slices with cardiac content
slice_range = None  # Optional: Set (start, end) to limit slice range
# 例如：加载 VTI 文件的代码


# Define tissue labels
class myLabels:
    def __init__(self, LV_wall, RV_wall, LA_wall, RA_wall, LV_blood, RV_blood, LA_blood, RA_blood,
                 body, muscle, brain, sinus, liver, gall_bladder, right_lung, left_lung,
                 esophagus, esophagus_contents, laryngopharynx, stomach_wall, stomach_contents,
                 pancreas, right_kidney_cortex, right_kidney_medulla, left_kidney_cortex,
                 left_kidney_medulla, adrenal, right_renal_pelvis, left_renal_pelvis,
                 spleen, ribs, cortical_bone, spine, spinal_cord, bone_marrow, arteries,
                 veins, bladder, prostate, ascending_intestine, transverse_intestine,
                 descending_intestine, small_intestine, rectum, seminal_vesicles,
                 vas_deferens, testes, epididymis, ejaculatory_duct, pericardium,
                 cartilage, intestine_air, ureter, urethra, lymph, lymph_abnormal,
                 trachea_bronchi, airways, thyroid, thymus):
        self.LV_wall = LV_wall
        self.RV_wall = RV_wall
        self.LA_wall = LA_wall
        self.RA_wall = RA_wall
        self.LV_blood = LV_blood
        self.RV_blood = RV_blood
        self.LA_blood = LA_blood
        self.RA_blood = RA_blood
        self.body = body
        self.muscle = muscle
        self.brain = brain
        self.sinus = sinus
        self.liver = liver
        self.gall_bladder = gall_bladder
        self.right_lung = right_lung
        self.left_lung = left_lung
        self.esophagus = esophagus
        self.esophagus_contents = esophagus_contents
        self.laryngopharynx = laryngopharynx
        self.stomach_wall = stomach_wall
        self.stomach_contents = stomach_contents
        self.pancreas = pancreas
        self.right_kidney_cortex = right_kidney_cortex
        self.right_kidney_medulla = right_kidney_medulla
        self.left_kidney_cortex = left_kidney_cortex
        self.left_kidney_medulla = left_kidney_medulla
        self.adrenal = adrenal
        self.right_renal_pelvis = right_renal_pelvis
        self.left_renal_pelvis = left_renal_pelvis
        self.spleen = spleen
        self.ribs = ribs
        self.cortical_bone = cortical_bone
        self.spine = spine
        self.spinal_cord = spinal_cord
        self.bone_marrow = bone_marrow
        self.arteries = arteries
        self.veins = veins
        self.bladder = bladder
        self.prostate = prostate
        self.ascending_intestine = ascending_intestine
        self.transverse_intestine = transverse_intestine
        self.descending_intestine = descending_intestine
        self.small_intestine = small_intestine
        self.rectum = rectum
        self.seminal_vesicles = seminal_vesicles
        self.vas_deferens = vas_deferens
        self.testes = testes
        self.epididymis = epididymis
        self.ejaculatory_duct = ejaculatory_duct
        self.Peri = pericardium
        self.cartilage = cartilage
        self.intestine_air = intestine_air
        self.ureter = ureter
        self.urethra = urethra
        self.lymph = lymph
        self.lymph_abnormal = lymph_abnormal
        self.trachea_bronchi = trachea_bronchi
        self.airways = airways
        self.thyroid = thyroid
        self.thymus = thymus

maskLabels = myLabels(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 
                     17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 
                     31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 
                     45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 
                     65, 66)

try:
    labels_array = img_ref.GetPointData().GetArray('labels')
    if labels_array is not None:
        # Reshape with original dimensions first, then transpose
        labels_original = vtk_to_numpy(labels_array).reshape(original_dims, order='F')
        labels = np.transpose(labels_original, (1, 2, 0))  # Transpose to get Z as last dimension
        data_input = labels.copy()
        print("Successfully loaded labels from file")
        print(f"Labels shape: {labels.shape}")
        print(f"Unique labels: {np.unique(labels)}")
    else:
        raise ValueError("No 'labels' array found")
except Exception as e:
    print(f"Warning: Could not load 'labels' array from file. Error: {e}")
    print("Creating placeholder labels...")
    labels = np.zeros(dim, dtype=np.uint8)
    data_input = labels.copy()
    print(f"Created placeholder labels with shape: {labels.shape}")

# Create tissue-specific masks
Target_mask = np.zeros_like(labels, dtype=np.uint8)
Blood_mask = np.zeros_like(labels, dtype=np.uint8)

# Populate masks based on cardiac labels
wall_labels = [maskLabels.LV_wall, maskLabels.RV_wall]
blood_labels = [maskLabels.LV_blood, maskLabels.RV_blood]

# Target mask (walls)
for label in wall_labels:
    Target_mask[labels == label] = 1
    
# Blood mask (debug)
for label in blood_labels:
    Blood_mask[labels == label] = 1
    
print(f"Wall voxels: {np.sum(Target_mask == 1)}")
print(f"Blood voxels: {np.sum(Blood_mask == 1)}")

# Now axis=(0,1) correctly sums over X and Y to find Z slices with cardiac content
original_z_indices = np.where(np.sum(Target_mask, axis=(0,1)) > 0)[0]
print(f"Found {len(original_z_indices)} slices containing cardiac structures: {original_z_indices}")

# Extend z_indices by adding 100 slices before and after
z_min = max(0, original_z_indices[0] - 50)  # Ensure we don't go below 0
z_max = min(labels.shape[2] - 1, original_z_indices[-1] + 50)  # Ensure we don't exceed volume size
extended_z_indices = np.arange(z_min, z_max + 1)

print(f"Original z-range: {original_z_indices[0]} to {original_z_indices[-1]} ({len(original_z_indices)} slices)")
print(f"Extended z-range: {z_min} to {z_max} ({len(extended_z_indices)} slices)")
print(f"Added {len(extended_z_indices) - len(original_z_indices)} slices")

# Use the extended range for processing
z_indices = extended_z_indices

# Filter slices based on configuration
if not process_all_slices and slice_range is not None:
    start_idx, end_idx = slice_range
    z_indices = [z for z in z_indices if start_idx <= z <= end_idx]
    print(f"Selected {len(z_indices)} slices in range {slice_range}")

# Preview slices (sample from beginning, middle, and end)
max_preview = min(15, len(z_indices))
if max_preview > 0:
    # Select slices from beginning, middle and end of the range
    if max_preview >= 3:
        # Fix: Create indices that won't go out of bounds
        indices = []
        if max_preview > 1:
            # Calculate safe step size
            step = (len(z_indices) - 1) // (max_preview - 1)
            for i in range(max_preview - 1):
                indices.append(i * step)
            # Add the last index
            indices.append(len(z_indices) - 1)
        else:
            indices = [0]
            
        # Get the actual z indices
        preview_indices = [z_indices[i] for i in indices]
    else:
        preview_indices = z_indices[:max_preview]
    
    fig, axes = plt.subplots(1, max_preview, figsize=(20, 4))
    for i, z in enumerate(preview_indices):
        if max_preview > 1:
            ax = axes[i]
        else:
            ax = axes
        combined = labels[:,:,z].copy()  # Use the actual labels
        # Highlight the cardiac structures
        cardiac_overlay = np.zeros_like(combined, dtype=float)
        cardiac_overlay[Target_mask[:,:,z] > 0] = 1  # Wall
        cardiac_overlay[Blood_mask[:,:,z] > 0] = 2   # Blood
        
        ax.imshow(combined, cmap='gray')
        ax.imshow(cardiac_overlay, alpha=0.4, cmap='jet')
        has_cardiac = z in original_z_indices
        ax.set_title(f"Slice {z} {'(cardiac)' if has_cardiac else ''}")
        ax.axis('off')
    plt.tight_layout()
    plt.show()
else:
    print("No cardiac slices found! Check your input file and labels.")

# 修复MMF导入问题的并行处理解决方案（带文件检查）

from joblib import Parallel, delayed
import numpy as np
import time
import os
import sys

def check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
    """
    检查指定slice是否已经完成处理
    
    Parameters:
    -----------
    slice_idx : int
        slice索引
    results_folder : str
        结果文件夹路径
    runWarper : bool
        是否运行warper
    runTexturizer : bool
        是否运行texturizer
        
    Returns:
    --------
    bool : True表示已完成，False表示需要处理
    """
    slice_results_folder = f"{results_folder}/slice_{slice_idx:03d}"
    slice_tissue_folder = f"{results_folder}/Tissue_properties_initial_frame/slice_{slice_idx:03d}"
    
    # 检查必要的文件是否存在
    required_files = []
    
    if runWarper:
        # 检查Model.vti文件
        model_file = os.path.join(slice_results_folder, "Model.vti")
        required_files.append(model_file)
        
    if runTexturizer:
        # 检查组织属性文件
        tissue_files = [
            os.path.join(slice_tissue_folder, "PD.vti"),
            os.path.join(slice_tissue_folder, "T1.vti"), 
            os.path.join(slice_tissue_folder, "T2.vti"),
            os.path.join(slice_tissue_folder, "T2s.vti")
        ]
        required_files.extend(tissue_files)
    
    # 检查所有必要文件是否存在且不为空
    for file_path in required_files:
        if not os.path.exists(file_path):
            return False
        if os.path.getsize(file_path) == 0:  # 检查文件不为空
            return False
            
    return True


def process_single_slice_fixed(slice_idx, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                              results_folder, warped_images, runWarper, runTexturizer, network_path,
                              voxelNumpyToVTK, parentVTI, force_reprocess=False):
    """
    修复MMF导入问题的并行处理函数（带文件检查）
    
    Parameters:
    -----------
    force_reprocess : bool, default=False
        是否强制重新处理，即使文件已存在
    """
    try:
        # 首先检查是否已经完成处理
        if not force_reprocess and check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
            print(f"✅ Slice {slice_idx} already processed, skipping...")
            return True
            
        print(f"Processing slice {slice_idx}...")

        # 强制设置Python路径
        import sys
        import os
        
        # 添加必要的路径到sys.path
        paths_to_add = [
            '/home/<USER>/XCAT_Project',  # 主项目路径
            '/home/<USER>/XCAT_Project/myWarpFunctions',  # myMorphingFunctions.py所在目录
            '/home/<USER>/XCAT_Project/myTexturizer'  # texturizationFunctions所在目录
        ]
        
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.insert(0, path)
        
        # 设置PYTHONPATH环境变量
        os.environ['PYTHONPATH'] = ':'.join(paths_to_add)
        
        # 导入myMorphingFunctions作为MMF
        try:
            import myMorphingFunctions as MMF
            from texturizationFunctions import defineTissueProperties, fixLVTexture
            print(f"成功导入MMF模块 for slice {slice_idx}")
        except ImportError as e:
            print(f"第一次导入失败: {e}")
            # 再次尝试，明确指定完整路径
            try:
                # 手动导入模块
                import importlib.util
                
                # 导入myMorphingFunctions
                mmf_path = '/home/<USER>/XCAT_Project/myWarpFunctions/myMorphingFunctions.py'
                spec = importlib.util.spec_from_file_location("myMorphingFunctions", mmf_path)
                MMF = importlib.util.module_from_spec(spec)
                sys.modules["myMorphingFunctions"] = MMF
                spec.loader.exec_module(MMF)
                
                # 导入texturizationFunctions
                tex_path = '/home/<USER>/XCAT_Project/myTexturizer/texturizationFunctions.py'
                spec = importlib.util.spec_from_file_location("texturizationFunctions", tex_path)
                tex_module = importlib.util.module_from_spec(spec)
                sys.modules["texturizationFunctions"] = tex_module
                spec.loader.exec_module(tex_module)
                
                defineTissueProperties = tex_module.defineTissueProperties
                fixLVTexture = tex_module.fixLVTexture
                
                print(f"通过importlib成功导入模块 for slice {slice_idx}")
            except Exception as e2:
                print(f"导入失败: {e2}")
                raise ImportError(f"无法导入必要的模块: {e2}")

        # 设置matplotlib为非交互式后端（避免GUI问题）
        import matplotlib
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt

        # Create slice-specific output directories
        slice_results_folder = f"{results_folder}/slice_{slice_idx:03d}"
        slice_warped_images = f"{warped_images}/slice_{slice_idx:03d}"
        slice_tissue_folder = f"{results_folder}/Tissue_properties_initial_frame/slice_{slice_idx:03d}"
        
        for folder in [slice_results_folder, slice_warped_images, slice_tissue_folder]:
            if not os.path.exists(folder):
                os.makedirs(folder, exist_ok=True)
        
        if runWarper:
            # Process slice
            slice_data = np.zeros((dim[0], dim[1], 1))
            slice_data[:,:,0] = labels[:,:,slice_idx]
            
            # 使用MMF的warpSlice函数
            data_input_slice, Target_mask_slice, BP_fill, mapx0, mapy0, centerBox_x, centerBox_y = MMF.warpSlice(
                Target_mask, data_input, slice_idx, maskLabels, True)
            
            # Save visualizations
            plt.figure(figsize=(8, 8))
            plt.imshow(data_input_slice[:,:,0])
            plt.savefig(slice_warped_images + "/Data_slice.png")
            plt.close()
            
            plt.figure(figsize=(8, 8))
            plt.imshow(Target_mask_slice[:,:,0], cmap='gray')
            plt.savefig(slice_warped_images + "/Target_mask_slice.png")
            plt.close()

            plt.figure(figsize=(8, 8))
            plt.imshow(BP_fill[:,:,0], cmap='gray')
            plt.savefig(slice_warped_images + "/BP_fill_slice.png")
            plt.close()
            
            plt.figure(figsize=(10, 10))
            plt.imshow(data_input_slice[:, :, 0], alpha=0.8)
            plt.imshow(Target_mask_slice[:, :, 0], cmap=plt.cm.gray, alpha=0.2)
            plt.savefig(f"{results_folder}/SAX_slices/Frame_slice_{slice_idx:03d}_01.png", 
                      bbox_inches='tight', dpi=400)
            plt.close()
            
            # VTK处理
            MMF.voxelNumpyToVTK = voxelNumpyToVTK
            MMF.parentVTI = parentVTI

            img_ref = MMF.vtkSliceImage(data_input_slice, spacing, 
                                       [orig_image[0], orig_image[1], orig_image[2] + slice_idx*spacing[2]])
            img_ref = MMF.addArrayToVtk(img_ref, data_input_slice, 'labels', False)
            img_ref = MMF.addArrayToVtk(img_ref, Target_mask_slice, 'LV_mask', False)
            
            model_path = os.path.join(slice_results_folder, "Model.vti")
            MMF.saveVTI(img_ref, model_path)
            print(f"Created Model.vti at: {model_path}")
            par_img = img_ref
            
            if runTexturizer:
                # 使用Texturizer生成组织属性
                PD0, T10, T20, T2s0 = defineTissueProperties(par_img, network_path)
                
                textureLV = False
                if not textureLV:
                    PD0, T10, T20, T2s0, LV_prop_vals = fixLVTexture(
                        data_input_slice, PD0, T10, T20, T2s0, 
                        maskLabels, which_prop='meanLV'
                    )
                
                # 清理不需要的arrays
                arrays_to_remove = ['dx_beating', 'dy_beating', 'dz_beating', 
                                   'dx_breathing', 'dy_breathing', 'dz_breathing', 
                                   'maskValues', 'parameters']
                for arr_n in arrays_to_remove:
                    if par_img.GetPointData().GetArray(arr_n):
                        par_img.GetPointData().RemoveArray(arr_n)
                        par_img.Modified()

                # 保存各个组织属性
                par_img = MMF.addArrayToVtk(par_img, PD0, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/PD.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T10, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T1.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T20, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T2.vti")
                
                par_img = MMF.addArrayToVtk(par_img, T2s0, 'parameters', False)
                MMF.saveVTI(par_img, slice_tissue_folder + "/T2s.vti")
                
                print(f"Slice {slice_idx} tissue properties:")
                print(f"  PD range: {np.min(PD0):.2f}-{np.max(PD0):.2f}")
                print(f"  T1 range: {np.min(T10):.2f}-{np.max(T10):.2f}")
                print(f"  T2 range: {np.min(T20):.2f}-{np.max(T20):.2f}")
                print(f"  T2* range: {np.min(T2s0):.2f}-{np.max(T2s0):.2f}")
                
                # 保存可视化
                plt.figure(figsize=(15, 5))
                
                plt.subplot(141)
                plt.imshow(PD0, cmap='viridis')
                plt.title('PD')
                plt.colorbar()
                
                plt.subplot(142) 
                plt.imshow(T10, cmap='plasma')
                plt.title('T1')
                plt.colorbar()
                
                plt.subplot(143)
                plt.imshow(T20, cmap='magma')
                plt.title('T2')
                plt.colorbar()
                
                plt.subplot(144)
                plt.imshow(T2s0, cmap='inferno')
                plt.title('T2*')
                plt.colorbar()
                
                plt.tight_layout()
                plt.savefig(slice_tissue_folder + "/tissue_properties.png", dpi=200)
                plt.close()
                
        print(f"✅ Successfully processed slice {slice_idx}")
        return True
        
    except Exception as e:
        print(f"❌ Error processing slice {slice_idx}: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def filter_unprocessed_slices(z_indices, results_folder, runWarper, runTexturizer):
    """
    过滤出还需要处理的slice索引
    
    Returns:
    --------
    tuple : (unprocessed_slices, already_completed_slices)
    """
    unprocessed = []
    completed = []
    
    for slice_idx in z_indices:
        if check_slice_completion(slice_idx, results_folder, runWarper, runTexturizer):
            completed.append(slice_idx)
        else:
            unprocessed.append(slice_idx)
    
    return unprocessed, completed


def run_parallel_processing(data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                           results_folder, warped_images, runWarper, runTexturizer, network_path,
                           z_indices, voxelNumpyToVTK, parentVTI, n_jobs=4, check_existing=True, 
                           force_reprocess=False):
    """
    运行并行处理，包含自动重试机制和文件检查
    
    Parameters:
    -----------
    check_existing : bool, default=True
        是否检查已存在的文件
    force_reprocess : bool, default=False
        是否强制重新处理所有文件，即使已存在
    """
    print(f"🚀 开始并行处理 {len(z_indices)} slices, 使用 {n_jobs} 个进程")
    print(f"确保已导入 myMorphingFunctions from /home/<USER>/XCAT_Project/myWarpFunctions/")
    
    # 检查已完成的文件
    if check_existing and not force_reprocess:
        print("\n📋 检查已存在的文件...")
        unprocessed_slices, completed_slices = filter_unprocessed_slices(
            z_indices, results_folder, runWarper, runTexturizer
        )
        
        print(f"   已完成: {len(completed_slices)} slices")
        print(f"   需处理: {len(unprocessed_slices)} slices")
        
        if completed_slices:
            print(f"   已完成的slices: {sorted(completed_slices)}")
            
        if not unprocessed_slices:
            print("🎉 所有slices都已完成，无需处理！")
            return [True] * len(z_indices)
            
        # 只处理未完成的slices
        z_indices_to_process = unprocessed_slices
    else:
        z_indices_to_process = z_indices
        completed_slices = []
    
    start_time = time.time()
    
    # 首先测试单个slice确保导入工作正常
    if z_indices_to_process:
        print(f"\n📋 测试单个slice以验证环境...")
        test_slice = sorted(z_indices_to_process)[0]
        test_result = process_single_slice_fixed(
            test_slice, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
            results_folder, warped_images, runWarper, runTexturizer, network_path,
            voxelNumpyToVTK, parentVTI, force_reprocess
        )
        
        if not test_result:
            print("❌ 测试slice失败！请检查环境配置。")
            return []
        
        print("✅ 测试成功！开始并行处理所有slices...")
        
        # 运行并行处理
        results = Parallel(n_jobs=n_jobs, verbose=10)(
            delayed(process_single_slice_fixed)(
                slice_idx, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                results_folder, warped_images, runWarper, runTexturizer, network_path,
                voxelNumpyToVTK, parentVTI, force_reprocess
            ) for slice_idx in sorted(z_indices_to_process)
        )
    else:
        results = []
    
    # 统计结果
    total_slices = len(z_indices)
    processed_slices = len(z_indices_to_process)
    successful_processed = sum(1 for r in results if r) if results else 0
    already_completed = len(completed_slices)
    total_successful = successful_processed + already_completed
    
    if z_indices_to_process:
        failed_slices = [z_indices_to_process[i] for i, r in enumerate(results) if not r]
    else:
        failed_slices = []
    
    total_time = time.time() - start_time
    
    print(f"\n🎉 === 并行处理完成 ===")
    print(f"📊 总统计:")
    print(f"   总时间: {total_time/60:.1f} 分钟")
    print(f"   总slices: {total_slices}")
    print(f"   已完成: {already_completed} (跳过)")
    print(f"   新处理: {processed_slices}")
    print(f"   新成功: {successful_processed}/{processed_slices}")
    print(f"   总成功: {total_successful}/{total_slices}")
    print(f"   总成功率: {total_successful/total_slices*100:.1f}%")
    if processed_slices > 0:
        print(f"   平均速度: {total_time/processed_slices:.1f} 秒/slice")
    
    if failed_slices:
        print(f"   失败的slices: {failed_slices}")
        
        # 尝试重新处理失败的slices（sequential）
        if len(failed_slices) < processed_slices * 0.2:  # 如果失败率小于20%
            print(f"\n🔄 尝试重新处理 {len(failed_slices)} 个失败的slices...")
            retry_results = []
            for slice_idx in failed_slices:
                print(f"重试 slice {slice_idx}...")
                result = process_single_slice_fixed(
                    slice_idx, data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                    results_folder, warped_images, runWarper, runTexturizer, network_path,
                    voxelNumpyToVTK, parentVTI, force_reprocess
                )
                retry_results.append(result)
            
            retry_successful = sum(1 for r in retry_results if r)
            print(f"重试结果: {retry_successful}/{len(failed_slices)} 成功")
    
    # 返回所有slices的结果状态
    all_results = []
    processed_idx = 0
    for slice_idx in z_indices:
        if slice_idx in completed_slices:
            all_results.append(True)  # 已完成
        else:
            all_results.append(results[processed_idx] if processed_idx < len(results) else False)
            processed_idx += 1
    
    return all_results


def run_best_available(data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                      results_folder, warped_images, runWarper, runTexturizer, network_path,
                      z_indices, voxelNumpyToVTK, parentVTI, try_parallel=True, n_jobs=4,
                      check_existing=True, force_reprocess=False):
    """
    自动选择最佳可用方案，优先使用并行处理
    
    Parameters:
    -----------
    check_existing : bool, default=True
        是否检查已存在的文件
    force_reprocess : bool, default=False
        是否强制重新处理所有文件
    """
    
    if try_parallel and len(z_indices) > 2:
        try:
            # 直接运行并行处理
            results = run_parallel_processing(
                data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
                results_folder, warped_images, runWarper, runTexturizer, network_path,
                z_indices, voxelNumpyToVTK, parentVTI, n_jobs, check_existing, force_reprocess
            )
            
            if results and sum(1 for r in results if r) > len(results) * 0.5:  # 至少50%成功
                return results
                
        except Exception as e:
            print(f"❌ 并行处理失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 如果并行失败，使用sequential（需要确保这个函数也有文件检查逻辑）
    print("🔄 使用Sequential处理...")
    # 这里需要你的sequential处理函数，也建议添加类似的文件检查逻辑
    # return run_optimized_sequential_final(...)
    
    # 暂时返回空结果，你需要实现或调用你的sequential函数
    print("❌ Sequential fallback not implemented")
    return []

# Define the missing parentVTI function
def parentVTI(img_slice, num_slices, size):
    """
    Create a parent VTI object containing the given slice.
    
    Parameters:
    img_slice (vtkImageData): The VTK image data object containing a single slice
    num_slices (int): Number of slices in Z dimension
    size (int): Size/resolution parameter
    
    Returns:
    vtkImageData: A new VTK image data object properly sized
    """
    import vtk
    from vtk.util.numpy_support import vtk_to_numpy, numpy_to_vtk
    
    # Create a new VTK image data object
    parent_img = vtk.vtkImageData()
    
    # Get dimensions and properties from slice
    dims = img_slice.GetDimensions()
    spacing = img_slice.GetSpacing()
    origin = img_slice.GetOrigin()
    
    # Set dimensions for the parent image (maintaining X,Y but setting Z to num_slices)
    parent_img.SetDimensions(dims[0], dims[1], num_slices)
    parent_img.SetSpacing(spacing)
    parent_img.SetOrigin(origin)
    
    # Copy all point data arrays from the slice
    pd = img_slice.GetPointData()
    for i in range(pd.GetNumberOfArrays()):
        array_name = pd.GetArrayName(i)
        array_data = vtk_to_numpy(pd.GetArray(i))
        
        # Reshape array to include all slices (replicate the same data)
        if len(array_data.shape) == 1:
            # Extend the flat array to cover all slices
            extended_array = np.tile(array_data, num_slices)
        else:
            # Handle multi-component arrays if present
            extended_array = np.tile(array_data, (num_slices, 1))
            
        # Create VTK array from extended data
        vtk_array = numpy_to_vtk(extended_array)
        vtk_array.SetName(array_name)
        parent_img.GetPointData().AddArray(vtk_array)
    
    return parent_img

# Add this function to the MMF module
MMF.parentVTI = parentVTI

# Define the missing function
def voxelNumpyToVTK(BP_fill, mapx, mapy):
    """Create a VTK image data object from a NumPy array and mapping coordinates.
    This replicates the functionality from the original script."""
    import vtk
    from vtk.util.numpy_support import numpy_to_vtk
    import numpy as np
    
    # Create VTK image data
    img = vtk.vtkImageData()
    img.SetDimensions(BP_fill.shape[0], BP_fill.shape[1], 1)
    img.SetSpacing(1.0, 1.0, 1.0)
    img.SetOrigin(0.0, 0.0, 0.0)
    
    # Add BP_fill data to the VTK image
    BP_fill_flat = BP_fill.reshape(-1, order='F')
    BP_vtk = numpy_to_vtk(BP_fill_flat)
    BP_vtk.SetName('labels')
    img.GetPointData().AddArray(BP_vtk)
    
    # Add mapping arrays if provided
    if mapx is not None and mapy is not None:
        mapx_flat = mapx.reshape(-1, order='F')
        mapx_vtk = numpy_to_vtk(mapx_flat)
        mapx_vtk.SetName('mapx')
        img.GetPointData().AddArray(mapx_vtk)
        
        mapy_flat = mapy.reshape(-1, order='F')
        mapy_vtk = numpy_to_vtk(mapy_flat)
        mapy_vtk.SetName('mapy')
        img.GetPointData().AddArray(mapy_vtk)
    
    return img

# Add this function to the MMF module for use in your code
MMF.voxelNumpyToVTK = voxelNumpyToVTK

# ============================================================================
# 使用示例
# ============================================================================

"""
# 基本使用（会自动跳过已完成的文件）
network_path = '/home/<USER>/XCAT_Project/myTexturizer/LAXsegnet'
results = run_parallel_processing(
    data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
    results_folder, warped_images, runWarper, runTexturizer, network_path,
    z_indices, voxelNumpyToVTK, parentVTI, 
    n_jobs=8,
    check_existing=True,  # 检查已存在的文件
    force_reprocess=False  # 不强制重新处理
)

# 强制重新处理所有文件
results = run_parallel_processing(
    data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
    results_folder, warped_images, runWarper, runTexturizer, network_path,
    z_indices, voxelNumpyToVTK, parentVTI, 
    n_jobs=8,
    force_reprocess=True  # 强制重新处理
)

# 使用自动选择（推荐）
results = run_best_available(
    data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
    results_folder, warped_images, runWarper, runTexturizer, network_path,
    z_indices, voxelNumpyToVTK, parentVTI, 
    try_parallel=True,
    n_jobs=8,
    check_existing=True,
    force_reprocess=False
)
"""

# 你原来的调用方式（现在会自动跳过已完成的文件）
# Binary file settings
orig_image = origin
targetImg_size_bin = [500, 500, 1]

network_path = '/home/<USER>/XCAT_Project/myTexturizer/LAXsegnet'
results = run_parallel_processing(
    data_input, labels, Target_mask, maskLabels, spacing, orig_image, dim,
    results_folder, warped_images, runWarper, runTexturizer, network_path,
    z_indices, voxelNumpyToVTK, parentVTI, 
    n_jobs=8,
    check_existing=True,    # 默认值，会检查已存在文件
    force_reprocess=False   # 默认值，不强制重新处理
)

# 在Format transformation cell的开头添加这段代码
import numpy as np

# Workaround for NumPy/VTK compatibility issue
# Fix for "numpy has no attribute 'bool'" error with newer NumPy versions
if not hasattr(np, 'bool'):
    np.bool = np.bool_
    print("✅ Applied NumPy compatibility fix for VTK conversion")

def check_binary_conversion_completion(slice_output_folder_bin, runTexturizer=True):
    """
    检查指定slice的binary转换是否已经完成
    
    Parameters:
    -----------
    slice_output_folder_bin : str
        binary输出文件夹路径
    runTexturizer : bool
        是否运行了texturizer（决定是否检查组织属性文件）
        
    Returns:
    --------
    bool : True表示已完成，False表示需要处理
    """
    if not os.path.exists(slice_output_folder_bin):
        return False
    
    # 检查必要的文件
    required_files = []
    
    # 检查labels.bin文件
    labels_file = os.path.join(slice_output_folder_bin, "labels.bin")
    required_files.append(labels_file)
    
    # 如果运行了texturizer，检查组织属性文件
    if runTexturizer:
        tissue_properties = ["pd", "t1", "t2", "t2s"]
        for prop in tissue_properties:
            prop_file = os.path.join(slice_output_folder_bin, prop, "value.bin")
            required_files.append(prop_file)
    
    # 检查所有必要文件是否存在且不为空
    for file_path in required_files:
        if not os.path.exists(file_path):
            return False
        if os.path.getsize(file_path) == 0:  # 检查文件不为空
            return False
            
    return True


def filter_unconverted_slices(z_indices, output_folder_bin, runTexturizer=True):
    """
    过滤出还需要转换的slice索引
    
    Returns:
    --------
    tuple : (unconverted_slices, already_converted_slices)
    """
    unconverted = []
    converted = []
    
    for slice_idx in z_indices:
        slice_output_folder_bin = f"{output_folder_bin}/slice_{slice_idx:03d}"
        if check_binary_conversion_completion(slice_output_folder_bin, runTexturizer):
            converted.append(slice_idx)
        else:
            unconverted.append(slice_idx)
    
    return unconverted, converted


# Convert to binary format (final step)

# Check if required variables exist
if 'z_indices' not in locals():
    print("❌ ERROR: z_indices not found! You need to run Cell 7 first to define z_indices.")
    print("Please run Cell 7 to set up z_indices before running this conversion.")
else:
    print(f"✅ z_indices found with {len(z_indices)} slices: {z_indices[:5]}...")

if 'runConverter' not in locals():
    print("❌ ERROR: runConverter not found! You need to run earlier cells to define runConverter.")
    runConverter = True  # Set default value
    print("Setting runConverter = True as default")

# 添加参数控制
check_existing_binary = True  # 是否检查已存在的binary文件
force_reconvert = False       # 是否强制重新转换

if runConverter and 'z_indices' in locals():
    print("\nChecking binary conversion status...")
    
    # 检查已完成的转换
    if check_existing_binary and not force_reconvert:
        print("\n📋 检查已存在的binary文件...")
        
        # 确定是否运行了texturizer（从已有变量或默认值）
        runTexturizer_for_check = locals().get('runTexturizer', True)
        
        unconverted_slices, converted_slices = filter_unconverted_slices(
            z_indices, output_folder_bin, runTexturizer_for_check
        )
        
        print(f"   已转换: {len(converted_slices)} slices")
        print(f"   需转换: {len(unconverted_slices)} slices")
        
        if converted_slices:
            print(f"   已转换的slices: {sorted(converted_slices)}")
            
        if not unconverted_slices:
            print("🎉 所有slices都已转换为binary格式，无需处理！")
            z_indices_to_convert = []  # 设置为空列表
        else:
            # 只转换未完成的slices
            z_indices_to_convert = unconverted_slices
    else:
        z_indices_to_convert = z_indices
        converted_slices = []
    
    if z_indices_to_convert or force_reconvert:
        if force_reconvert:
            z_indices_to_convert = z_indices
            print("🔄 强制重新转换所有文件...")
        else:
            print(f"\n🔄 开始转换 {len(z_indices_to_convert)} 个slices到binary格式...")
        
        # First, ensure the vti2Bin function is available and correct
        def vti2Bin_v2(input_folder, output_folder, target_size):
            """
            Convert VTI files to binary format with proper subfolder structure.
            
            Args:
                input_folder: Folder containing VTI files
                output_folder: Destination folder for binary files
                target_size: Target dimensions for binary files [width, height, depth]
            """
            print(f"Converting VTI files from {input_folder} to binary in {output_folder}")
            
            # Ensure output folder exists
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
            
            # Model file (mandatory)
            model_file = os.path.join(input_folder, "Model.vti")
            if os.path.exists(model_file):
                # Read model file
                reader = vtk.vtkXMLImageDataReader()
                reader.SetFileName(model_file)
                reader.Update()
                model_data = reader.GetOutput()
                
                # Extract label data
                if model_data.GetPointData().HasArray('labels'):
                    label_array = vtk_to_numpy(model_data.GetPointData().GetArray('labels'))
                    label_dims = model_data.GetDimensions()
                    labels = label_array.reshape(label_dims[0], label_dims[1], label_dims[2], order='F')
                    
                    # Resize to target dimensions if needed
                    if labels.shape[0] != target_size[0] or labels.shape[1] != target_size[1]:
                        from scipy.ndimage import zoom
                        zoom_factor = (target_size[0]/labels.shape[0], 
                                    target_size[1]/labels.shape[1],
                                    target_size[2]/labels.shape[2])
                        labels = zoom(labels, zoom_factor, order=0)  # Order 0 for nearest neighbor
                    
                    # Save as binary file (directly in output folder)
                    labels = labels.astype(np.uint8)
                    labels.tofile(os.path.join(output_folder, "labels.bin"))
                    print(f"✓ Saved labels.bin ({labels.shape})")
                else:
                    print("✗ No 'labels' array found in Model.vti")
            else:
                print(f"✗ Model file not found: {model_file}")
                
            # Process tissue property files, look in both possible locations
            tissue_folder = os.path.join(os.path.dirname(input_folder), "Tissue_properties_initial_frame", os.path.basename(input_folder))
            
            for prop in ["PD", "T1", "T2", "T2s"]:
                # Create property-specific subfolder
                prop_output_folder = os.path.join(output_folder, prop.lower())
                if not os.path.exists(prop_output_folder):
                    os.makedirs(prop_output_folder)
                
                # Try main results folder first
                prop_file = os.path.join(input_folder, f"{prop}.vti")
                
                # If not found, try tissue properties folder
                if not os.path.exists(prop_file):
                    prop_file = os.path.join(tissue_folder, f"{prop}.vti")
                
                if os.path.exists(prop_file):
                    print(f"Found {prop}.vti in {os.path.dirname(prop_file)}")
                    # Read property file
                    reader = vtk.vtkXMLImageDataReader()
                    reader.SetFileName(prop_file)
                    reader.Update()
                    prop_data = reader.GetOutput()
                    
                    # Extract property data - try both 'parameters' and property name
                    array_name = None
                    if prop_data.GetPointData().HasArray('parameters'):
                        array_name = 'parameters'
                    elif prop_data.GetPointData().HasArray(prop):
                        array_name = prop
                        
                    if array_name:
                        prop_array = vtk_to_numpy(prop_data.GetPointData().GetArray(array_name))
                        prop_dims = prop_data.GetDimensions()
                        prop_values = prop_array.reshape(prop_dims[0], prop_dims[1], prop_dims[2], order='F')
                        
                        # Resize to target dimensions if needed
                        if prop_values.shape[0] != target_size[0] or prop_values.shape[1] != target_size[1]:
                            from scipy.ndimage import zoom
                            zoom_factor = (target_size[0]/prop_values.shape[0], 
                                        target_size[1]/prop_values.shape[1],
                                        target_size[2]/prop_values.shape[2])
                            prop_values = zoom(prop_values, zoom_factor, order=1)  # Order 1 for linear interpolation
                        
                        # Save as binary file in property-specific subfolder
                        prop_values = prop_values.astype(np.float32)
                        output_file = os.path.join(prop_output_folder, "value.bin")
                        prop_values.tofile(output_file)
                        print(f"✓ Saved {prop.lower()}/value.bin ({prop_values.shape})")
                    else:
                        print(f"✗ No suitable array found in {prop}.vti")
                else:
                    print(f"✗ Property file not found: {prop_file} (also checked in {tissue_folder})")
        
        # Add vti2Bin to MMF if it doesn't exist (or replace if it's incorrect)
        MMF.vti2Bin_v2 = vti2Bin_v2
        
        # 统计变量
        conversion_start_time = time.time()
        successful_conversions = 0
        failed_conversions = 0
        
        # Now run the conversion
        for SAX_slice_selection in z_indices_to_convert:
            slice_output_folder_bin = f"{output_folder_bin}/slice_{SAX_slice_selection:03d}"
            slice_results_folder = f"{results_folder}/slice_{SAX_slice_selection:03d}"
            
            # 检查是否需要跳过（如果不是强制转换）
            if not force_reconvert and check_binary_conversion_completion(slice_output_folder_bin, locals().get('runTexturizer', True)):
                print(f"✅ Slice {SAX_slice_selection} already converted, skipping...")
                successful_conversions += 1
                continue
            
            print(f"\nConverting slice {SAX_slice_selection} to binary format...")
            
            # Create output directory if it doesn't exist
            if not os.path.exists(slice_output_folder_bin):
                os.makedirs(slice_output_folder_bin)
                
            # Binary conversion with detailed error handling
            try:
                # Check if input files exist
                model_file = os.path.join(slice_results_folder, "Model.vti")
                if not os.path.exists(model_file):
                    print(f"✗ Model.vti not found in {slice_results_folder}")
                    failed_conversions += 1
                else:
                    # Use correct target size
                    MMF.vti2Bin_v2(slice_results_folder, slice_output_folder_bin, [500, 500, 1])
                    
                    # 验证转换是否成功
                    if check_binary_conversion_completion(slice_output_folder_bin, locals().get('runTexturizer', True)):
                        print(f"✓ Slice {SAX_slice_selection} converted successfully")
                        successful_conversions += 1
                    else:
                        print(f"✗ Slice {SAX_slice_selection} conversion incomplete")
                        failed_conversions += 1
                        
            except Exception as e:
                print(f"✗ Error converting slice {SAX_slice_selection}: {str(e)}")
                failed_conversions += 1
                import traceback
                traceback.print_exc()  # Print detailed error information
        
        # 输出统计信息
        conversion_time = time.time() - conversion_start_time
        total_slices = len(z_indices)
        already_converted = len(converted_slices) if not force_reconvert else 0
        total_successful = successful_conversions + already_converted
        
        print(f"\n🎉 === Binary转换完成 ===")
        print(f"📊 总统计:")
        print(f"   转换时间: {conversion_time/60:.1f} 分钟")
        print(f"   总slices: {total_slices}")
        if not force_reconvert:
            print(f"   已完成: {already_converted} (跳过)")
        print(f"   新转换: {len(z_indices_to_convert)}")
        print(f"   成功: {successful_conversions}")
        print(f"   失败: {failed_conversions}")
        print(f"   总成功: {total_successful}/{total_slices}")
        print(f"   总成功率: {total_successful/total_slices*100:.1f}%")
        
        if failed_conversions > 0:
            print(f"   ⚠️ 有 {failed_conversions} 个slices转换失败，请检查日志")
    
    print("\nAll processing complete!")

# ============================================================================
# 手动控制参数（可以在运行前修改这些值）
# ============================================================================

"""
# 如果想要强制重新转换所有文件，设置：
force_reconvert = True

# 如果想要跳过检查直接转换，设置：
check_existing_binary = False

# 示例用法：
# 1. 正常使用（自动跳过已转换的）- 默认行为
# 2. 强制重新转换所有文件：
#    force_reconvert = True
# 3. 不检查已存在文件，直接转换：
#    check_existing_binary = False
"""

# Load and visualize binary tissue property maps
import os
import numpy as np

# For Jupyter notebooks, use this magic command instead:
%matplotlib inline
import matplotlib.pyplot as plt

# Alternative: Remove backend specification entirely and let matplotlib choose
# import matplotlib.pyplot as plt

from scipy.ndimage import gaussian_filter


# Select a slice to visualize (use one of your processed slices)
slice_number = 700 # Use one of your successfully processed slices
# slice_folder = f"./outputData/test_male169/bin_output/slice_{slice_number:03d}"
slice_folder = f'{output_folder_bin}/slice_{slice_number:03d}'

# Function to load binary files
def load_bin_file(filepath, dtype=np.float32):
    """Load binary file and reshape to 2D"""
    if not os.path.exists(filepath):
        print(f"Error: File not found: {filepath}")
        return np.zeros((500, 500))
    
    data = np.fromfile(filepath, dtype=dtype)
    
    # Reshape to 500x500 (assuming these are the dimensions)
    try:
        data = data.reshape(500, 500)
    except ValueError as e:
        print(f"Error reshaping data from {filepath}: {e}")
        # Return zeros if reshape fails
        return np.zeros((500, 500))
    
    return data

# Load tissue property maps
labels = load_bin_file(os.path.join(slice_folder, "labels.bin"), dtype=np.uint8)
pd_map = load_bin_file(os.path.join(slice_folder, "pd", "value.bin"))
t1_map = load_bin_file(os.path.join(slice_folder, "t1", "value.bin"))
t2_map = load_bin_file(os.path.join(slice_folder, "t2", "value.bin"))
t2star_map = load_bin_file(os.path.join(slice_folder, "t2s", "value.bin"))

# Print statistics to verify data
print(f"PD map range: {np.min(pd_map):.4f} to {np.max(pd_map):.4f}")
print(f"T1 map range: {np.min(t1_map):.4f} to {np.max(t1_map):.4f}")
print(f"T2 map range: {np.min(t2_map):.4f} to {np.max(t2_map):.4f}")
print(f"T2* map range: {np.min(t2star_map):.4f} to {np.max(t2star_map):.4f}")

# Convert normalized values to physical values (if needed)
# These ranges are typical for 3T MRI
pd_physical = pd_map  # Already normalized 0-1
t1_physical = t1_map   # Scale to 0-3000ms
t2_physical = t2_map    # Scale to 0-300ms
t2star_physical = t2star_map  # Scale to 0-150ms
# t1_physical = t1_map * 3000  # Scale to 0-3000ms
# t2_physical = t2_map * 300   # Scale to 0-300ms
# t2star_physical = t2star_map * 150  # Scale to 0-150ms

# Apply light smoothing to remove artifacts
def smooth_map(map_data, sigma=0.5):
    return gaussian_filter(map_data, sigma)

pd_map_filtered = smooth_map(pd_physical)
t1_map_filtered = smooth_map(t1_physical)
t2_map_filtered = smooth_map(t2_physical)
t2star_map_filtered = smooth_map(t2star_physical)

# Function to resize images to fixed square dimensions
def resize_to_square_fixed(image, target_size=500):
    """Resize image to a fixed square size"""
    from scipy.ndimage import zoom
    
    if image.shape[0] == target_size and image.shape[1] == target_size:
        return image
        
    zoom_factor = (target_size / image.shape[0], target_size / image.shape[1])
    resized = zoom(image, zoom_factor, order=1)
    return resized

# Function to simulate MRI signals from tissue property maps
def simulate_mri_signal(pd_map, t1_map, t2_map, t2star_map, TR, TE, flip_angle, sequence_type='spin_echo'):
    """
    Generate synthetic MRI images from tissue property maps
    
    Parameters:
    -----------
    pd_map, t1_map, t2_map, t2star_map : 2D arrays
        Tissue property maps
    TR : float
        Repetition time in ms
    TE : float
        Echo time in ms
    flip_angle : float
        Flip angle in degrees
    sequence_type : str
        'spin_echo', 'gradient_echo', or 'bssfp'
        
    Returns:
    --------
    image : 2D array
        Simulated MR image
    """
    # Convert flip angle to radians if provided in degrees
    if flip_angle > 6.28:  # If larger than 2π, assume it's in degrees
        flip_angle = np.deg2rad(flip_angle)
    
    # Create image containers
    signal = np.zeros_like(pd_map, dtype=float)
    
    # Add small epsilon to avoid division by zero
    eps = 1e-6
    
    if sequence_type.lower() == 'spin_echo':
        # T2-weighted spin echo: S = PD * (1-exp(-TR/T1)) * exp(-TE/T2)
        t1_factor = 1 - np.exp(-TR / (t1_map + eps))
        t2_factor = np.exp(-TE / (t2_map + eps))
        signal = pd_map * t1_factor * t2_factor
        
    elif sequence_type.lower() == 'gradient_echo':
        # Gradient echo: S = PD * sin(α) * (1-exp(-TR/T1)) / (1-cos(α)*exp(-TR/T1)) * exp(-TE/T2*)
        t1_factor = np.sin(flip_angle) * (1 - np.exp(-TR / (t1_map + eps)))
        denominator = 1 - np.cos(flip_angle) * np.exp(-TR / (t1_map + eps))
        t2star_factor = np.exp(-TE / (t2star_map + eps))
        signal = pd_map * (t1_factor / (denominator + eps)) * t2star_factor
        
    elif sequence_type.lower() == 'bssfp':
        # Simplified bSSFP equation
        num = pd_map * np.sin(flip_angle) * (1 - np.exp(-TR / t1_map))
        denom = 1 - np.cos(flip_angle) * np.exp(-TR / t1_map)
        t2_factor = np.exp(-TE / (t2_map + eps))
        signal = (num / (denom + eps)) * t2_factor
    
    return signal

# Create synthetic MR images with different sequence parameters
# 1. T1-weighted
tr_t1 = 500    # Short TR to enhance T1 contrast
te_t1 = 10     # Short TE to minimize T2 effects
fa_t1 = 90     # 90° flip angle

# 2. T2-weighted
tr_t2 = 2500   # Long TR to reduce T1 effects
te_t2 = 80     # Long TE to enhance T2 contrast
fa_t2 = 90     # 90° flip angle

# 3. T2*-weighted
tr_t2star = 600  # Medium TR
te_t2star = 25   # Medium-long TE for T2* contrast
fa_t2star = 20   # Low flip angle

# 4. FLAIR (T2 with T1 recovery suppression)
tr_flair = 8000  # Very long TR
te_flair = 100   # Long TE for T2 contrast
fa_flair = 90    # 90° flip angle

# Generate the synthetic MR images
t1w_image = simulate_mri_signal(pd_map_filtered, t1_map_filtered, t2_map_filtered, t2star_map_filtered, 
                               tr_t1, te_t1, fa_t1, 'spin_echo')

t2w_image = simulate_mri_signal(pd_map_filtered, t1_map_filtered, t2_map_filtered, t2star_map_filtered, 
                               tr_t2, te_t2, fa_t2, 'spin_echo')

t2star_image = simulate_mri_signal(pd_map_filtered, t1_map_filtered, t2_map_filtered, t2star_map_filtered, 
                                  tr_t2star, te_t2star, fa_t2star, 'gradient_echo')

# Normalize images for visualization
def normalize_for_display(image):
    """Normalize image to 0-1 range for display"""
    if np.max(image) == np.min(image):
        return np.zeros_like(image)
    return (image - np.min(image)) / (np.max(image) - np.min(image))

t1w_norm = normalize_for_display(t1w_image)
t2w_norm = normalize_for_display(t2w_image)
t2star_norm = normalize_for_display(t2star_image)

# Display tissue property maps
plt.figure(figsize=(18, 10))

# First row: Property maps
plt.subplot(2, 4, 1)
plt.imshow(labels, cmap='tab20')
plt.title(f"Labels - Slice {slice_number}")
plt.colorbar()

plt.subplot(2, 4, 2)
plt.imshow(pd_physical, cmap='viridis')
plt.title("Proton Density")
plt.colorbar()

plt.subplot(2, 4, 3)
plt.imshow(t1_physical, cmap='plasma')
plt.title("T1 (ms)")
plt.colorbar()

plt.subplot(2, 4, 4)
plt.imshow(t2_physical, cmap='magma')
plt.title("T2 (ms)")
plt.colorbar()

# Second row: Simulated MR images
plt.subplot(2, 4, 5)
plt.imshow(t2star_physical, cmap='inferno')
plt.title("T2* (ms)")
plt.colorbar()

plt.subplot(2, 4, 6)
plt.imshow(t1w_norm, cmap='gray')
plt.title(f"T1-weighted\nTR={tr_t1}ms, TE={te_t1}ms")
plt.colorbar()

plt.subplot(2, 4, 7)
plt.imshow(t2w_norm, cmap='gray')
plt.title(f"T2-weighted\nTR={tr_t2}ms, TE={te_t2}ms")
plt.colorbar()

plt.subplot(2, 4, 8)
plt.imshow(t2star_norm, cmap='gray')
plt.title(f"T2*-weighted\nTR={tr_t2star}ms, TE={te_t2star}ms")
plt.colorbar()

plt.tight_layout()
plt.show()

# Explore tissue contrast
plt.figure(figsize=(12, 8))
tissue_mask = (labels > 0)  # All tissues
blood_mask = (pd_map < 0.4) & tissue_mask  # Approximate blood pool
myocardium_mask = (pd_map > 0.6) & (pd_map < 0.9) & tissue_mask  # Approximate myocardium

# Show ROI masks
plt.subplot(2, 3, 1)
plt.imshow(tissue_mask, cmap='gray')
plt.title("All Tissue Mask")

plt.subplot(2, 3, 2)
plt.imshow(blood_mask, cmap='gray')
plt.title("Blood Pool Mask")

plt.subplot(2, 3, 3)
plt.imshow(myocardium_mask, cmap='gray')
plt.title("Myocardium Mask")

# Calculate tissue contrast
t1w_contrast = np.mean(t1w_image[myocardium_mask]) - np.mean(t1w_image[blood_mask])
t2w_contrast = np.mean(t2w_image[myocardium_mask]) - np.mean(t2w_image[blood_mask])
t2star_contrast = np.mean(t2star_image[myocardium_mask]) - np.mean(t2star_image[blood_mask])

print(f"Contrast (myocardium - blood):")
print(f"T1w: {t1w_contrast:.4f}")
print(f"T2w: {t2w_contrast:.4f}")
print(f"T2*w: {t2star_contrast:.4f}")

# Show contrast difference visualization
plt.subplot(2, 3, 4)
plt.imshow(t1w_norm * tissue_mask, cmap='gray')
plt.title("T1w Image (masked)")

plt.subplot(2, 3, 5)
plt.imshow(t2w_norm * tissue_mask, cmap='gray')
plt.title("T2w Image (masked)")

plt.subplot(2, 3, 6)
plt.imshow(t2star_norm * tissue_mask, cmap='gray')
plt.title("T2*w Image (masked)")

plt.tight_layout()
plt.show()

import os
import numpy as np
# import matplotlib.pyplot as plt
from ipywidgets import interact, IntSlider, Dropdown
# import matplotlib.colors as mcolors
# Import required for interactive widgets 
from ipywidgets import interactive_output, HBox, VBox

def load_parameter_map_binary(base_folder, slice_num, param_name):
    """Load parameter map from binary file"""
    # Handle special case for labels
    if param_name.lower() == "labels":
        # Labels are directly in the slice folder
        bin_file = os.path.join(base_folder, f"bin_output/slice_{slice_num:03d}/labels.bin")
    else:
        # Convert parameter name to lowercase for file paths
        param_lower = param_name.lower()
        if param_lower == "t2*":
            param_lower = "t2s"
            
        # Construct path to binary file
        bin_folder = f"{base_folder}/bin_output/slice_{slice_num:03d}/{param_lower}"
        bin_file = os.path.join(bin_folder, "value.bin")
    
    if not os.path.exists(bin_file):
        print(f"File not found: {bin_file}")
        return None
    
    try:
        # Read binary file - assuming float32 for parameters, uint8/int16 for labels
        dtype = np.uint8 if param_name.lower() == "labels" else np.float32
        data = np.fromfile(bin_file, dtype=dtype)
        
        # Calculate dimensions based on array size
        size = data.size
        
        # For 250,000 elements, reshape to 500x500
        if size == 250000:
            data = data.reshape(500, 500)
        # For other common sizes
        elif size == 256000:
            data = data.reshape(640, 400)
        elif size == 262144:
            data = data.reshape(512, 512)
        else:
            # Try to find a perfect square
            dim = int(np.sqrt(size))
            if dim * dim == size:
                data = data.reshape(dim, dim)
            else:
                print(f"Warning: Using best guess for dimensions with array size {size}")
                # Try to find reasonable dimensions
                factors = []
                for i in range(1, int(np.sqrt(size)) + 1):
                    if size % i == 0:
                        factors.append((i, size // i))
                # Use the most square-like dimensions
                best_ratio = float('inf')
                best_dims = None
                for h, w in factors:
                    ratio = max(h/w, w/h)
                    if ratio < best_ratio:
                        best_ratio = ratio
                        best_dims = (h, w)
                
                if best_dims:
                    data = data.reshape(best_dims)
                else:
                    print(f"Cannot determine shape for array of size {size}")
                    return None
            
        return data
    except Exception as e:
        print(f"Error loading binary file {bin_file}: {e}")
        return None

def create_3d_parameter_maps_binary(base_folder, z_indices):
    """Create 3D parameter maps from all binary files"""
    # Try to determine dimensions from first available slice
    first_slice = None
    first_slice_idx = None
    
    for idx in z_indices:
        test_slice = load_parameter_map_binary(base_folder, idx, "PD")
        if test_slice is not None:
            first_slice = test_slice
            first_slice_idx = idx
            break
    
    if first_slice is None:
        print("No valid slices found!")
        return None, None, None, None, None
    
    print(f"Using dimensions from slice {first_slice_idx}: {first_slice.shape}")
    
    # Create empty 3D volumes
    nx, ny = first_slice.shape
    nz = len(z_indices)
    
    pd_volume = np.zeros((nx, ny, nz))
    t1_volume = np.zeros((nx, ny, nz))
    t2_volume = np.zeros((nx, ny, nz))
    t2s_volume = np.zeros((nx, ny, nz))
    labels_volume = np.zeros((nx, ny, nz), dtype=np.uint8)
    
    # Fill with actual data
    valid_slices = 0
    for i, idx in enumerate(z_indices):
        try:
            slice_pd = load_parameter_map_binary(base_folder, idx, "PD")
            slice_t1 = load_parameter_map_binary(base_folder, idx, "T1")
            slice_t2 = load_parameter_map_binary(base_folder, idx, "T2")
            slice_t2s = load_parameter_map_binary(base_folder, idx, "T2*")
            slice_labels = load_parameter_map_binary(base_folder, idx, "labels")
            
            # Check if parameter maps are available
            params_ok = slice_pd is not None and slice_t1 is not None and slice_t2 is not None and slice_t2s is not None
            
            # We'll still use the slice if at least the labels or one parameter is available
            if params_ok or slice_labels is not None:
                # Fill parameters if available
                if params_ok and all(s.shape == first_slice.shape for s in [slice_pd, slice_t1, slice_t2, slice_t2s]):
                    pd_volume[:, :, i] = slice_pd
                    t1_volume[:, :, i] = slice_t1
                    t2_volume[:, :, i] = slice_t2
                    t2s_volume[:, :, i] = slice_t2s
                
                # Fill labels if available
                if slice_labels is not None and slice_labels.shape == first_slice.shape:
                    labels_volume[:, :, i] = slice_labels
                    
                valid_slices += 1
            else:
                print(f"Warning: Missing parameter maps for slice {idx}")
        except Exception as e:
            print(f"Error processing slice {idx}: {e}")
    
    print(f"Successfully loaded parameter maps from {valid_slices} slices")
    return pd_volume, t1_volume, t2_volume, t2s_volume, labels_volume

# Base folder should be the main output directory
# base_folder = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/outputData/test_male169"
base_folder = results_folder

# Load all parameter maps into 3D volumes
pd_volume, t1_volume, t2_volume, t2s_volume, labels_volume = create_3d_parameter_maps_binary(base_folder, z_indices)

print(f"Created 3D parameter maps with dimensions: {pd_volume.shape}")

# Create parameter dictionary
params = {
    'PD': pd_volume,
    'T1': t1_volume,
    'T2': t2_volume,
    'T2*': t2s_volume,
    'Labels': labels_volume
}


# import numpy as np
import matplotlib.pyplot as plt
# import cv2
# from tqdm import tqdm
# import os
from scipy.ndimage import zoom

def update_t2star_with_b0_3d(t2s_volume, t2_volume, b0_volume, voxel_size=2.0):
    """
    Update T2* values based on B0 field inhomogeneities using full 3D gradients
    
    Parameters:
    -----------
    t2s_volume : 3D array
        Intrinsic T2* values in milliseconds
    t2_volume : 3D array
        Intrinsic T2 values in milliseconds
    b0_volume : 3D array
        B0 field inhomogeneities in Hz
        
    Returns:
    --------
    t2star_volume : 3D array
        Updated T2* values in milliseconds
    """
    t2s_sec = t2s_volume / 1000.0
    # Calculate 1/T2* (original)
    r2_star_intial= 1.0 / (t2s_sec + 1e-10)

    # Convert T2 from ms to seconds
    t2_sec = t2_volume / 1000.0
    
    # Calculate 1/T2 (intrinsic relaxation rate)
    r2 = 1.0 / (t2_sec + 1e-10)

    #assuming original T2 star considering R2' contribution from diffusion
    r2prime_diffusion = r2_star_intial - r2 
    # r2prime_diffusion = 0
    
    # Scale the B0 field (multiply by 2 as you had in previous code)
    # b0_volume = b0_volume * 2
    
    # Convert voxel size to meters
    voxel_size_m = voxel_size / 1000.0
    
    # Calculate 3D B0 gradients using central differences (all directions)
    print("Calculating 3D field gradients...")
    gz, gy, gx = np.gradient(b0_volume, voxel_size_m)
    
    # Gradient magnitude in Hz/m (combining all directions)
    g_magnitude = np.sqrt(gx**2 + gy**2 + gz**2)
    
    # Constants
    gamma = 42.58e6  # gyromagnetic ratio in Hz/T
    D = 3e-9        # Diffusion coefficient of water at 37°C (m²/s)
    
    # Calculate R2' contribution from static dephasing
    r2prime_static = np.sqrt((g_magnitude * voxel_size_m)**2 / 12.0)
    print(f'complex calculation to get the range of r2prime-static: {np.min(r2prime_static):.2f}-{np.max(r2prime_static):.2f}')

    # r2prime_static = np.abs(b0_volume)
    r2prime_static = (np.pi * g_magnitude * voxel_size_m) / np.sqrt(6) *1.5
    print(f'simple calculation to get the range of r2prime-static: {np.min(r2prime_static):.2f}-{np.max(r2prime_static):.2f}')

    # Calculate R2' contribution from diffusion
    # r2prime_diffusion = (g_magnitude)**2 * D / 12.0
    
    # Total R2' is the sum of static and diffusion contributions
    r2prime_total = r2prime_static + r2prime_diffusion
    
    # Total R2* = R2 + R2'
    r2_star = r2 + r2prime_total
    
    # Convert back to T2* in milliseconds
    t2star_sec = 1.0 / (r2_star + 1e-10)
    t2star_ms = t2star_sec * 1000.0
    
    # Apply reasonable limits
    t2star_ms = np.clip(t2star_ms, 1.0, 66.0)
    
    return t2star_ms

def process_t2star_volume_3d(t2s_volume, t2_volume, b0_map_path, output_folder=None, z_indices=None, labels_volume=None, 
                           b0_start_slice=199, b0_end_slice=306):
    """
    Process the entire T2* volume with B0 field inhomogeneities using full 3D processing
    
    Parameters:
    -----------
    t2s_volume : 3D numpy array
        The original T2* volume
    t2_volume : 3D numpy array
        The original T2 volume
    b0_map_path : str
        Path to the B0 map file (.npy)
    output_folder : str, optional
        If provided, saves updated T2* maps back to binary files
    z_indices : list, optional
        Original slice indices corresponding to the volume slices
    labels_volume : 3D numpy array, optional
        Volume of tissue labels for visualization
    b0_start_slice : int
        Starting slice index in B0 map that corresponds to the heart region
    b0_end_slice : int
        Ending slice index in B0 map that corresponds to the heart region
    
    Returns:
    --------
    updated_t2s_volume : 3D numpy array
        The updated T2* volume with B0 effects
    """
    # Load the B0 map volume
    b0_map_all = np.load(b0_map_path)
    print(f"Original B0 map shape: {b0_map_all.shape}")
    
    # Apply transpose to align with T2* volume
    b0_map_all = np.transpose(b0_map_all, [1, 0, 2])
    # print(f"Transposed B0 map shape: {b0_map_all.shape}")
    
    # Check if requested B0 slice range is valid
    if b0_end_slice >= b0_map_all.shape[2] or b0_start_slice < 0:
        print(f"WARNING: Requested B0 slice range ({b0_start_slice}-{b0_end_slice}) out of bounds!")
        print(f"Using full range (0-{b0_map_all.shape[2]-1}) instead.")
        b0_start_slice = 0
        b0_end_slice = b0_map_all.shape[2] - 1
    
    # Extract the relevant portion of the B0 map
    print(f"Extracting B0 map slices {b0_start_slice} to {b0_end_slice}")
    b0_heart = b0_map_all[:, :, b0_start_slice:b0_end_slice+1]
    
    # Resize B0 map to match T2* volume dimensions if needed
    print(f"T2* volume shape: {t2s_volume.shape}")
    print(f"Extracted B0 map shape: {b0_heart.shape}")
    
    if b0_heart.shape != t2s_volume.shape:
        print("Resizing B0 map to match T2* volume dimensions...")
        zoom_factors = [
            t2s_volume.shape[0] / b0_heart.shape[0],
            t2s_volume.shape[1] / b0_heart.shape[1],
            t2s_volume.shape[2] / b0_heart.shape[2]
        ]
        b0_heart_resized = zoom(b0_heart, zoom_factors, order=1)
        print(f"Resized B0 map shape: {b0_heart_resized.shape}")
    else:
        b0_heart_resized = b0_heart
    
    # Apply transpose to each slice to match orientations
    print("Applying orientation adjustments to B0 map...")
    for z in range(b0_heart_resized.shape[2]):
        b0_heart_resized[:, :, z] = np.transpose(b0_heart_resized[:, :, z]) / 2
    
    # Process the entire volume at once using 3D gradients
    print("Processing entire T2* volume with 3D B0 effects...")
    updated_t2s_volume = update_t2star_with_b0_3d(t2s_volume, t2_volume, b0_heart_resized)
    
    # Save individual slices to binary files if output folder is provided
    if output_folder and z_indices is not None:
        print("Saving updated T2*+ slices to binary files...")
        for i in tqdm(range(t2s_volume.shape[2]), desc="Saving slices"):
            if i >= len(z_indices):
                print(f"WARNING: z_indices doesn't have enough entries (needed: {i}, have: {len(z_indices)})")
                break
                
            original_slice_idx = z_indices[i]
            slice_folder = f"{output_folder}/slice_{original_slice_idx:03d}/t2s"
            os.makedirs(slice_folder, exist_ok=True)
            bin_file = os.path.join(slice_folder, "value.bin")
            updated_t2s_volume[:, :, i].astype(np.float32).tofile(bin_file)
    
    # Show multiple views for visualization (sagittal, coronal, axial)
    visualize_3d_results(t2s_volume, b0_heart_resized, updated_t2s_volume, labels_volume)
    
    print(f"Processing complete. Original T2* range: {np.min(t2s_volume):.2f}-{np.max(t2s_volume):.2f} ms")
    print(f"Updated T2* range: {np.min(updated_t2s_volume):.2f}-{np.max(updated_t2s_volume):.2f} ms")
    
    return updated_t2s_volume, b0_heart_resized # Return the updated T2* volume and B0 map

def visualize_3d_results(t2s_volume, b0_volume, updated_t2s_volume, labels_volume=None):
    """
    Create comprehensive visualizations of 3D processing results
    showing multiple views (sagittal, coronal, axial) with vertical colorbars
    """
    # Get dimensions
    sx, sy, sz = t2s_volume.shape
    
    # Define slice positions for each view (middle of each dimension)
    sag_slice = sz // 2  # Sagittal - YZ plane
    cor_slice = sy // 2  # Coronal - XZ plane
    ax_slice = sx // 2   # Axial - XY plane
    
    # Create figure with three views - wider to accommodate vertical colorbars
    fig, axes = plt.subplots(4, 3, figsize=(20, 20))
    
    # Row titles
    views = ['Sagittal', 'Coronal', 'Axial']
    
    # First row: Original T2* maps
    for i, (view, slice_idx) in enumerate(zip(views, [sag_slice, cor_slice, ax_slice])):
        if view == 'Sagittal':
            data = t2s_volume[:, :, slice_idx]
        elif view == 'Coronal':
            data = t2s_volume[:, slice_idx, :]
        else:  # Axial
            data = t2s_volume[slice_idx, :, :]
            
        im = axes[0, i].imshow(data, cmap='hot', vmin=0, vmax=100)
        axes[0, i].set_title(f"Original T2* - {view}")
        axes[0, i].axis('off')
        # Add vertical colorbar for each subplot
        plt.colorbar(im, ax=axes[0, i], orientation='vertical', shrink=0.8, pad=0.02, label='T2* (ms)')
    
    # Second row: B0 field maps
    for i, (view, slice_idx) in enumerate(zip(views, [sag_slice, cor_slice, ax_slice])):
        if view == 'Sagittal':
            data = b0_volume[:, :, slice_idx]
        elif view == 'Coronal':
            data = b0_volume[:, slice_idx, :]
        else:  # Axial
            data = b0_volume[slice_idx, :, :]
            
        im = axes[1, i].imshow(data, cmap='coolwarm')
        axes[1, i].set_title(f"B0 Field - {view}")
        axes[1, i].axis('off')
        # Add vertical colorbar
        plt.colorbar(im, ax=axes[1, i], orientation='vertical', shrink=0.8, pad=0.02, label='B0 Field (Hz)')
    
    # Third row: Updated T2* maps
    for i, (view, slice_idx) in enumerate(zip(views, [sag_slice, cor_slice, ax_slice])):
        if view == 'Sagittal':
            data = updated_t2s_volume[:, :, slice_idx]
        elif view == 'Coronal':
            data = updated_t2s_volume[:, slice_idx, :]
        else:  # Axial
            data = updated_t2s_volume[slice_idx, :, :]
            
        im = axes[2, i].imshow(data, cmap='hot', vmin=0, vmax=100)
        axes[2, i].set_title(f"Updated T2* - {view}")
        axes[2, i].axis('off')
        # Add vertical colorbar
        plt.colorbar(im, ax=axes[2, i], orientation='vertical', shrink=0.8, pad=0.02, label='T2* (ms)')
    
    # Fourth row: Tissue labels
    if labels_volume is not None:
        for i, (view, slice_idx) in enumerate(zip(views, [sag_slice, cor_slice, ax_slice])):
            if view == 'Sagittal':
                data = labels_volume[:, :, slice_idx]
            elif view == 'Coronal':
                data = labels_volume[:, slice_idx, :]
            else:  # Axial
                data = labels_volume[slice_idx, :, :]
                
            unique_labels = np.unique(data)
            n_labels = len(unique_labels)
            if n_labels <= 10:
                cmap = plt.cm.get_cmap('tab10', n_labels)
                im = axes[3, i].imshow(data, cmap=cmap)
            else:
                im = axes[3, i].imshow(data, cmap='viridis')
            axes[3, i].set_title(f"Tissue Labels - {view}")
            axes[3, i].axis('off')
            # Add vertical colorbar
            plt.colorbar(im, ax=axes[3, i], orientation='vertical', shrink=0.8, pad=0.02, label='Tissue Label')
    else:
        for i in range(3):
            axes[3, i].text(0.5, 0.5, "Labels not provided", ha='center', va='center', transform=axes[3, i].transAxes)
            axes[3, i].axis('off')
    
    plt.tight_layout()
    plt.subplots_adjust(hspace=0.3, wspace=0.3)  # Add more space between plots
    plt.show()
    
    # Show 3D gradient magnitude - Second figure
    gz, gy, gx = np.gradient(b0_volume)
    g_magnitude_3d = np.sqrt(gx**2 + gy**2 + gz**2)
    
    # Calculate effect ratio
    effect_ratio = updated_t2s_volume / (t2s_volume + 0.001)
    
    # Create visualization of gradient magnitude and effect
    fig, axes = plt.subplots(3, 3, figsize=(20, 15))
    
    # Show gradient magnitude in three views
    for i, (view, slice_idx) in enumerate(zip(views, [sag_slice, cor_slice, ax_slice])):
        if view == 'Sagittal':
            data = g_magnitude_3d[:, :, slice_idx]
        elif view == 'Coronal':
            data = g_magnitude_3d[:, slice_idx, :]
        else:  # Axial
            data = g_magnitude_3d[slice_idx, :, :]
            
        im = axes[0, i].imshow(data, cmap='viridis')
        axes[0, i].set_title(f"B0 Gradient Magnitude - {view}")
        axes[0, i].axis('off')
        # Add vertical colorbar
        plt.colorbar(im, ax=axes[0, i], orientation='vertical', shrink=0.8, pad=0.02, label='Gradient (Hz/m)')
    
    # Show effect ratio in three views
    for i, (view, slice_idx) in enumerate(zip(views, [sag_slice, cor_slice, ax_slice])):
        if view == 'Sagittal':
            data = effect_ratio[:, :, slice_idx]
        elif view == 'Coronal':
            data = effect_ratio[:, slice_idx, :]
        else:  # Axial
            data = effect_ratio[slice_idx, :, :]
            
        im = axes[1, i].imshow(data, cmap='RdBu_r', vmin=0.5, vmax=1.5)
        axes[1, i].set_title(f"T2* Modification Ratio - {view}")
        axes[1, i].axis('off')
        # Add vertical colorbar
        plt.colorbar(im, ax=axes[1, i], orientation='vertical', shrink=0.8, pad=0.02, label='Effect Ratio (T2*/T2)')
    
    # Show absolute difference
    for i, (view, slice_idx) in enumerate(zip(views, [sag_slice, cor_slice, ax_slice])):
        if view == 'Sagittal':
            data = updated_t2s_volume[:, :, slice_idx] - t2s_volume[:, :, slice_idx]
        elif view == 'Coronal':
            data = updated_t2s_volume[:, slice_idx, :] - t2s_volume[:, slice_idx, :]
        else:  # Axial
            data = updated_t2s_volume[slice_idx, :, :] - t2s_volume[slice_idx, :, :]
            
        im = axes[2, i].imshow(data, cmap='coolwarm', vmin=-20, vmax=20)
        axes[2, i].set_title(f"T2* Change (ms) - {view}")
        axes[2, i].axis('off')
        # Add vertical colorbar
        plt.colorbar(im, ax=axes[2, i], orientation='vertical', shrink=0.8, pad=0.02, label='T2* Change (ms)')
    
    plt.tight_layout()
    plt.subplots_adjust(hspace=0.3, wspace=0.3)  # Add more space between plots
    plt.show()

# Main execution
# Path to the B0 map file
b0_map_path = '/home/<USER>/XCAT_Project/output/male_169/male_169_field_Hz_20250610_211706.npy'

# Output folder for saving the updated T2* maps
# output_folder = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/outputData/test_male169/bin_output"

# Process the entire T2* volume with full 3D processing
updated_t2s_volume, b0_heart_resized = process_t2star_volume_3d(
    t2s_volume,
    t2_volume, 
    b0_map_path, 
    output_folder_bin, 
    z_indices, 
    labels_volume=params['Labels'],
    b0_start_slice=z_min,  # Start of heart region in B0 map
    b0_end_slice=z_max     # End of heart region in B0 map
)

# Replace the original T2* volume with the updated one
params['T2*+'] = updated_t2s_volume

from ipywidgets import interact
import ipywidgets as widgets
import numpy as np
from scipy import ndimage
import matplotlib.pyplot as plt

# Define the tissue types to display with their corresponding label values
tissue_types = {
    'LV Wall': maskLabels.LV_wall,
    'RV Wall': maskLabels.RV_wall,
    'LA Wall': maskLabels.LA_wall,  
    'RA Wall': maskLabels.RA_wall,
    'LV Blood': maskLabels.LV_blood,
    'RV Blood': maskLabels.RV_blood,
    'LA Blood': maskLabels.LA_blood,
    'RA Blood': maskLabels.RA_blood,
    'Pericardium': maskLabels.Peri,
    'Arteries': maskLabels.arteries,
    'veins': maskLabels.veins,
    'Right Lung': maskLabels.right_lung,
    'Left Lung': maskLabels.left_lung,
    'Liver': maskLabels.liver,
    'Spleen': maskLabels.spleen,
    'Pancreas': maskLabels.pancreas,
    'Spine': maskLabels.spine,
    'Spinal Cord': maskLabels.spinal_cord,
    'muscle': maskLabels.muscle,
    'body': maskLabels.body,
    'coritcal bone': maskLabels.cortical_bone,
    'ribs': maskLabels.ribs,
    'bone marrow': maskLabels.bone_marrow,
}

def clean_rv_wall_mask(data):
    """
    Filter RV wall mask to keep connected components that are adjacent to RV blood
    Uses scipy for much faster processing
    
    Parameters:
    -----------
    data : ndarray
        3D array with tissue labels where 2=RV wall and 6=RV blood
    
    Returns:
    --------
    ndarray
        Binary mask of cleaned RV wall
    """
    # Create masks for RV wall and blood
    rv_wall = (data == 2)
    rv_blood = (data == 6)
    
    # Find connected components using scipy (MUCH faster than manual flood fill)
    wall_components, num_components = ndimage.label(rv_wall)
    
    print(f"Found {num_components} RV wall components")
    
    # Create cleaned mask
    cleaned_rv_wall = np.zeros_like(rv_wall)
    
    # Create a dilated blood mask to check adjacency more efficiently
    # This is faster than checking every voxel's neighbors
    dilated_blood = ndimage.binary_dilation(rv_blood)
    
    # Check each component for adjacency to blood
    for label in range(1, num_components + 1):
        component = (wall_components == label)
        
        # Check if this component overlaps with dilated blood
        # (meaning it's adjacent to blood)
        if np.any(component & dilated_blood):
            cleaned_rv_wall |= component
    
    return cleaned_rv_wall

def create_tissue_groups(data):
    # Initialize masks for each group
    cardiac_mask = np.zeros_like(data, dtype=bool)
    lung_mask = np.zeros_like(data, dtype=bool)
    tissue_mask = np.zeros_like(data, dtype=bool)
    air_mask = np.zeros_like(data, dtype=bool)
    
    # Clean RV wall mask
    cleaned_rv_wall = clean_rv_wall_mask(data)
    
    # Cardiac tissues (labels 1, 5, 6 and cleaned label 2)
    cardiac_labels = [1, 3, 4, 5, 6, 7, 8, 50] #include pericardiac region
    for label in cardiac_labels:
        cardiac_mask |= (data == label)
    # Add cleaned RV wall
    cardiac_mask |= cleaned_rv_wall

    # FIXED: Add removed parts of RV wall to tissue mask
    removed_rv_wall = (data == 2) & ~cleaned_rv_wall
    tissue_mask |= removed_rv_wall
    
    # Lung (label 15,16)
    lung_labels = [15, 16]
    for label in lung_labels:
        lung_mask |= (data == label)
    
    # All other labeled tissues
    all_labels = np.unique(data)
    other_tissue_labels = [label for label in all_labels 
                          if label not in cardiac_labels + [0, 2, 15, 16]]
    for label in other_tissue_labels:
        tissue_mask |= (data == label)
    
    # Air (unlabeled regions, label 0)
    air_mask = (data == 0)
    
    return cardiac_mask, lung_mask, tissue_mask, air_mask


# First, compute the cleaned RV wall mask using your existing function:
cleaned_rv = clean_rv_wall_mask(data_input)

# Create a copy of the original data_input:
updated_data_input = data_input.copy()

# Identify the voxels that were originally RV wall (label 2) but are removed in the cleaning process.
removed_rv_wall = (data_input == 2) & (~cleaned_rv)

# Determine the body label from tissue_types; change the key to match your definition.
body_label = tissue_types.get('body', 9)  # fallback to 9 if 'body' is not defined

# Update these voxels to body
updated_data_input[removed_rv_wall] = body_label

# Create the tissue group masks
cardiac_mask, lung_mask, tissue_mask, air_mask = create_tissue_groups(data_input)

# FIX: Convert best_slice to integer
best_slice = int((z_min + z_max) // 2)

# Visualization
plt.figure(figsize=(15, 10))

# Plot all four masks
plt.subplot(221)
plt.imshow(cardiac_mask[:,:,best_slice], cmap='gray')
plt.title('Cardiac Tissues\n(Labels 1,2,5,6)')
plt.axis('off')

plt.subplot(222)
plt.imshow(lung_mask[:,:,best_slice], cmap='gray')
plt.title('Lung Tissue\n(Label 16)')
plt.axis('off')

plt.subplot(223)
plt.imshow(tissue_mask[:,:,best_slice], cmap='gray')
plt.title('Other Tissues\n(Remaining Labels)')
plt.axis('off')

plt.subplot(224)
plt.imshow(air_mask[:,:,best_slice], cmap='gray')
plt.title('Air\n(Label 0)')
plt.axis('off')

plt.tight_layout()
plt.show()

# Print volume statistics
print("\nVolume statistics:")
print(f"Cardiac tissues: {cardiac_mask.sum()} voxels")
print(f"Lung tissue: {lung_mask.sum()} voxels")
print(f"Other tissues: {tissue_mask.sum()} voxels")
print(f"Air: {air_mask.sum()} voxels")

# Interactive slice viewer for all masks
def update_group_viewer(slice_idx):
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Plot cardiac mask
    axes[0,0].imshow(cardiac_mask[:,:,slice_idx], cmap='gray')
    axes[0,0].set_title('Cardiac Tissues\n(Labels 1,2,5,6)')
    axes[0,0].axis('off')
    
    # Plot lung mask
    axes[0,1].imshow(lung_mask[:,:,slice_idx], cmap='gray')
    axes[0,1].set_title('Lung Tissue\n(Label 16)')
    axes[0,1].axis('off')
    
    # Plot other tissues mask
    axes[1,0].imshow(tissue_mask[:,:,slice_idx], cmap='gray')
    axes[1,0].set_title('Other Tissues\n(Remaining Labels)')
    axes[1,0].axis('off')
    
    # Plot air mask
    axes[1,1].imshow(air_mask[:,:,slice_idx], cmap='gray')
    axes[1,1].set_title('Air\n(Label 0)')
    axes[1,1].axis('off')
    
    plt.tight_layout()
    plt.show()


# Create interactive slider
# interact(update_group_viewer, 
#         slice_idx=widgets.IntSlider(
#             min=0, 
#             max=data_input.shape[2]-1, 
#             step=1, 
#             value=best_slice,
#             description='Slice:',
#             continuous_update=False
#         ))

def compare_rv_wall_masks(data):
    """Create interactive viewer to compare original and cleaned RV wall masks"""
    original_rv_wall = (data == 2)
    cleaned_rv_wall = clean_rv_wall_mask(data)
    
    def update_comparison(slice_idx):
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Plot original RV wall mask
        axes[0].imshow(original_rv_wall[:,:,slice_idx], cmap='gray')
        axes[0].set_title(f'Original RV Wall\nSlice {slice_idx}\n{original_rv_wall[:,:,slice_idx].sum()} pixels')
        axes[0].axis('off')
        
        # Plot RV blood for reference
        axes[1].imshow((data[:,:,slice_idx] == 6), cmap='gray')
        axes[1].set_title(f'RV Blood\nSlice {slice_idx}')
        axes[1].axis('off')
        
        # Plot cleaned RV wall mask
        axes[2].imshow(cleaned_rv_wall[:,:,slice_idx], cmap='gray')
        axes[2].set_title(f'Cleaned RV Wall\nSlice {slice_idx}\n{cleaned_rv_wall[:,:,slice_idx].sum()} pixels')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # Print statistics for this slice
        print(f"\nSlice {slice_idx} statistics:")
        print(f"Original RV wall pixels: {original_rv_wall[:,:,slice_idx].sum()}")
        print(f"Cleaned RV wall pixels: {cleaned_rv_wall[:,:,slice_idx].sum()}")
        print(f"Removed pixels: {original_rv_wall[:,:,slice_idx].sum() - cleaned_rv_wall[:,:,slice_idx].sum()}")
    
    # interact(update_comparison, 
    #         slice_idx=widgets.IntSlider(
    #             min=0, 
    #             max=data_input.shape[2]-1, 
    #             step=1, 
    #             value=best_slice,
    #             description='Slice:',
    #             continuous_update=False
    #         ))
    
    # Print overall statistics
    print("\nOverall statistics:")
    print(f"Total original RV wall voxels: {original_rv_wall.sum()}")
    print(f"Total cleaned RV wall voxels: {cleaned_rv_wall.sum()}")
    print(f"Total removed voxels: {original_rv_wall.sum() - cleaned_rv_wall.sum()}")
    print(f"Percentage of RV wall removed: {(1 - cleaned_rv_wall.sum()/original_rv_wall.sum())*100:.1f}%")


# Run the comparison
# compare_rv_wall_masks(data_input)

import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import lsq_linear
from matplotlib import patches

# Load the cardiac mask and B0 map
# cardiac_mask = np.load('/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/output/male_169/male_169_cardiac_box_mask_20250610_211706.npy')
# cardiac_mask = np.transpose(cardiac_mask, [1,2,0])

print(f'Size of cardiac_mask: {cardiac_mask.shape}')
b0_map = np.load('/home/<USER>/XCAT_Project/output/male_169/male_169_field_Hz_20250610_211706.npy') # Assuming b0_map_path is already defined
print(f"Original B0 map and mask shape: {b0_map.shape}")
# Ensure the dimensions match
assert cardiac_mask.shape == b0_map.shape, "Cardiac mask and B0 map must have the same dimensions"
b0_map = b0_map - np.median(b0_map[cardiac_mask==1])

def generate_sh_basis_functions(volume_shape):
    """
    Generate 2nd order spherical harmonic basis functions
    
    Parameters:
    -----------
    volume_shape : tuple
        Shape of the 3D volume (nx, ny, nz)
    
    Returns:
    --------
    dict
        Dictionary of 3D arrays representing spherical harmonic basis functions
    """
    nx, ny, nz = volume_shape
    
    # Create normalized coordinate grid (-1 to 1)
    # For proper shimming orientation: x = Right/Left, y = Anterior/Posterior, z = Superior/Inferior
    x = np.linspace(-1, 1, nx)
    y = np.linspace(-1, 1, ny)
    z = np.linspace(-1, 1, nz)
    
    # Create the meshgrid with the correct orientation for shimming
    # Using 'ij' indexing to ensure correct orientation
    X, Y, Z = np.meshgrid(x, y, z, indexing='ij')
    
    # Define basis functions up to 2nd order
    basis = {}
    
    # 0th order
    basis['Z0'] = np.ones(volume_shape)  # Constant term
    
    # 1st order
    basis['X'] = X  # X (R/L)
    basis['Y'] = Y  # Y (A/P)
    basis['Z'] = Z  # Z (S/I)
    
    # 2nd order
    basis['Z2'] = Z**2 - (1/2)*(X**2 + Y**2)  # Z²-1/2(X²+Y²)
    basis['ZX'] = Z * X                        # ZX
    basis['ZY'] = Z * Y                        # ZY
    basis['C2'] = X**2 - Y**2                  # X²-Y²
    basis['S2'] = 2 * X * Y                    # 2XY
    
    return basis

# Generate basis functions with proper orientation for shimming
basis_functions = generate_sh_basis_functions(b0_map.shape)

# Define coefficient names for 2nd order shimming
coeff_names = ['Z0', 'X', 'Y', 'Z', 'Z2', 'ZX', 'ZY', 'C2', 'S2']

# Define sensitivities
sensitivities = {
    'Z0': 6058,
    'X': 1,
    'Y': 1,
    'Z': 1,
    'Z2': 6.942,
    'ZX': 24.15,
    'ZY': 24.15,
    'C2': 3.64,
    'S2': 3.64
}

# Only consider voxels within the mask
mask_indices = np.where(cardiac_mask == 1)
num_voxels = len(mask_indices[0])
print(f"Number of voxels in cardiac mask: {num_voxels}")

# Prepare matrix and vector for least squares fitting
# Matrix A: each column is a basis function flattened in the mask region
# Vector b: the B0 field flattened in the mask region

A = np.zeros((num_voxels, len(coeff_names)))
b = np.zeros(num_voxels)

for i, name in enumerate(coeff_names):
    # Extract mask region from basis function and flatten
    A[:, i] = basis_functions[name][mask_indices]

# Extract mask region from B0 map and flatten
b = -b0_map[mask_indices]  # Negative sign because we want to compensate field inhomogeneity

# Set constraints (optional)
# Here we can set upper and lower bounds for the shim currents
lower_bounds = np.full(len(coeff_names), -np.inf)
upper_bounds = np.full(len(coeff_names), np.inf)

# Use least squares optimization
result = lsq_linear(A, b, bounds=(lower_bounds, upper_bounds), method='trf', lsmr_tol='auto', verbose=1)
optimal_coeff_array = result.x

# Convert optimized coefficient array to dictionary
optimal_coefficients = {name: value for name, value in zip(coeff_names, optimal_coeff_array)}

# Calculate shim field
shim_field = np.zeros_like(b0_map, dtype=float)
for name, coeff in optimal_coefficients.items():
    shim_field += coeff * basis_functions[name]

# Calculate shimmed B0 field
shimmed_b0_map = b0_map + shim_field

# Calculate RMS before and after shimming in the cardiac region
original_rms = np.sqrt(np.mean((b0_map[cardiac_mask == 1] - np.mean(b0_map[cardiac_mask == 1]))**2))
shimmed_rms = np.sqrt(np.mean((shimmed_b0_map[cardiac_mask == 1] - np.mean(shimmed_b0_map[cardiac_mask == 1]))**2))

print(f"Original B0 field RMS in cardiac region: {original_rms:.2f} Hz")
print(f"Shimmed B0 field RMS in cardiac region: {shimmed_rms:.2f} Hz")
print(f"Improvement: {(1 - shimmed_rms/original_rms)*100:.1f}%")

# Print optimized shim coefficients
print("\nOptimized shim coefficients:")
for name, coeff in optimal_coefficients.items():
    print(f"{name}: {coeff:.4f}")

# Calculate current needed for each coil
print("\nRequired currents for shimming:")
for name, coeff in optimal_coefficients.items():
    if name in sensitivities:
        current = coeff / sensitivities[name]
        print(f"{name}: {current:.4f} A")

# Function to find bounding box of the cardiac region
def find_cardiac_box(mask):
    """
    Find bounding box around the cardiac region
    
    Parameters:
    -----------
    mask : ndarray
        Binary mask of the cardiac region
    
    Returns:
    --------
    tuple
        ((xmin, xmax), (ymin, ymax), (zmin, zmax)) coordinates of bounding box
    """
    # Find the coordinates of non-zero elements (cardiac tissues)
    coords = np.where(mask)
    
    if len(coords[0]) == 0:  # No cardiac tissues found
        return None
    
    # Get the min and max coordinates to create bounding box
    xmin, xmax = np.min(coords[0]), np.max(coords[0])
    ymin, ymax = np.min(coords[1]), np.max(coords[1])
    zmin, zmax = np.min(coords[2]), np.max(coords[2])
    
    # Add a margin of 5 voxels around the box (or less if near the boundary)
    margin = 5
    xmin = max(0, xmin - margin)
    ymin = max(0, ymin - margin)
    zmin = max(0, zmin - margin)
    xmax = min(mask.shape[0] - 1, xmax + margin)
    ymax = min(mask.shape[1] - 1, ymax + margin)
    zmax = min(mask.shape[2] - 1, zmax + margin)
    
    return ((xmin, xmax), (ymin, ymax), (zmin, zmax))

# Visualization function
def visualize_fields(original_field, shim_field, shimmed_field, mask, slice_idx=None):
    """
    Visualize original, shim, and shimmed B0 fields focused on the cardiac region
    
    Parameters:
    -----------
    original_field : ndarray
        Original B0 field
    shim_field : ndarray
        Shim field
    shimmed_field : ndarray
        Shimmed B0 field
    mask : ndarray
        Binary mask
    slice_idx : int, optional
        Index of slice to display, defaults to middle slice in cardiac region
    """
    # Find cardiac box
    cardiac_box = find_cardiac_box(mask)
    if cardiac_box is None:
        print("No cardiac tissue found in mask")
        return None
    
    (xmin, xmax), (ymin, ymax), (zmin, zmax) = cardiac_box
    
    # Set default slice to middle of cardiac region if not specified
    if slice_idx is None:
        slice_idx = (zmin + zmax) // 2
    
    fig = plt.figure(figsize=(15, 10))
    
    # Original B0 field - full view
    ax1 = fig.add_subplot(231)
    im1 = ax1.imshow(original_field[:,:,slice_idx], cmap='jet', vmin=-200, vmax=200)
    ax1.set_title('Original B0 Field (Hz)')
    ax1.axis('off')
    fig.colorbar(im1, ax=ax1, fraction=0.046, pad=0.04)
    
    # Add cardiac box outline
    rect1 = patches.Rectangle((ymin, xmin), ymax-ymin, xmax-xmin, 
                             linewidth=2, edgecolor='r', facecolor='none')
    ax1.add_patch(rect1)
    
    # Shim field - full view
    ax2 = fig.add_subplot(232)
    im2 = ax2.imshow(shim_field[:,:,slice_idx], cmap='jet', vmin=-200, vmax=200)
    ax2.set_title('Shim Field (Hz)')
    ax2.axis('off')
    fig.colorbar(im2, ax=ax2, fraction=0.046, pad=0.04)
    
    # Add cardiac box outline
    rect2 = patches.Rectangle((ymin, xmin), ymax-ymin, xmax-xmin, 
                             linewidth=2, edgecolor='r', facecolor='none')
    ax2.add_patch(rect2)
    
    # Shimmed B0 field - full view
    ax3 = fig.add_subplot(233)
    im3 = ax3.imshow(shimmed_field[:,:,slice_idx], cmap='jet', vmin=-200, vmax=200)
    ax3.set_title('Shimmed B0 Field (Hz)')
    ax3.axis('off')
    fig.colorbar(im3, ax=ax3, fraction=0.046, pad=0.04)
    
    # Add cardiac box outline
    rect3 = patches.Rectangle((ymin, xmin), ymax-ymin, xmax-xmin, 
                             linewidth=2, edgecolor='r', facecolor='none')
    ax3.add_patch(rect3)
    
    # Original B0 field - zoomed to cardiac region
    ax4 = fig.add_subplot(234)
    im4 = ax4.imshow(original_field[xmin:xmax+1, ymin:ymax+1, slice_idx], cmap='jet', vmin=-200, vmax=200)
    ax4.set_title('Original B0 Field - Cardiac Region (Hz)')
    ax4.axis('off')
    fig.colorbar(im4, ax=ax4, fraction=0.046, pad=0.04)
    
    # Show mask overlay on zoomed region
    mask_overlay = np.ma.masked_where(mask[xmin:xmax+1, ymin:ymax+1, slice_idx] == 0, 
                                     mask[xmin:xmax+1, ymin:ymax+1, slice_idx])
    ax4.imshow(mask_overlay, cmap='autumn', alpha=0.3)
    
    # Shim field - zoomed to cardiac region
    ax5 = fig.add_subplot(235)
    im5 = ax5.imshow(shim_field[xmin:xmax+1, ymin:ymax+1, slice_idx], cmap='jet', vmin=-200, vmax=200)
    ax5.set_title('Shim Field - Cardiac Region (Hz)')
    ax5.axis('off')
    fig.colorbar(im5, ax=ax5, fraction=0.046, pad=0.04)
    
    # Show mask overlay on zoomed region
    ax5.imshow(mask_overlay, cmap='autumn', alpha=0.3)
    
    # Shimmed B0 field - zoomed to cardiac region
    ax6 = fig.add_subplot(236)
    im6 = ax6.imshow(shimmed_field[xmin:xmax+1, ymin:ymax+1, slice_idx], cmap='jet', vmin=-200, vmax=200)
    ax6.set_title('Shimmed B0 Field - Cardiac Region (Hz)')
    ax6.axis('off')
    fig.colorbar(im6, ax=ax6, fraction=0.046, pad=0.04)
    
    # Show mask overlay on zoomed region
    ax6.imshow(mask_overlay, cmap='autumn', alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Create histogram figure
    fig2 = plt.figure(figsize=(15, 5))
    
    # Calculate masked histograms
    masked_original = original_field[mask == 1]
    masked_shimmed = shimmed_field[mask == 1]
    
    # Histogram of B0 field in masked region (before)
    ax1 = fig2.add_subplot(121)
    ax1.hist(masked_original, bins=50, alpha=0.7, color='blue')
    ax1.set_title(f'Original Field Histogram\nRMS: {original_rms:.2f} Hz')
    ax1.set_xlabel('Field (Hz)')
    ax1.set_ylabel('Voxel Count')
    
    # Histogram of B0 field in masked region (after)
    ax2 = fig2.add_subplot(122)
    ax2.hist(masked_shimmed, bins=50, alpha=0.7, color='green')
    ax2.set_title(f'Shimmed Field Histogram\nRMS: {shimmed_rms:.2f} Hz')
    ax2.set_xlabel('Field (Hz)')
    ax2.set_ylabel('Voxel Count')
    
    plt.tight_layout()
    plt.show()
    
    return fig, fig2

# Create interactive slice viewer for visualization
def interactive_field_viewer(original_field, shim_field, shimmed_field, mask):
    """Create interactive slice viewer to examine fields"""
    
    from ipywidgets import interact, IntSlider
    
    # Find cardiac box
    cardiac_box = find_cardiac_box(mask)
    if cardiac_box is None:
        print("No cardiac tissue found in mask")
        return None
    
    (_, _), (_, _), (zmin, zmax) = cardiac_box
    
    def update_view(slice_idx):
        return visualize_fields(original_field, shim_field, shimmed_field, mask, slice_idx)
    
    interact(update_view, 
             slice_idx=IntSlider(
                 min=zmin, 
                 max=zmax, 
                 step=1, 
                 value=(zmin + zmax) // 2,
                 description='Slice:',
                 continuous_update=False
             ))

# Visualize results
static_figs = visualize_fields(b0_map, shim_field, shimmed_b0_map, cardiac_mask)

# Create interactive viewer 
try:
    from ipywidgets import interact, IntSlider
    interactive_field_viewer(b0_map, shim_field, shimmed_b0_map, cardiac_mask)
except ImportError:
    print("IPython widgets not available for interactive visualization.")

# Save the result if needed
# np.save('shimmed_b0_map.npy', shimmed_b0_map)
# np.save('shim_field.npy', shim_field)
# np.save('shim_coefficients.npy', np.array(list(optimal_coefficients.values())))

# Crop all parameter maps to a 300×300×300 region centered on the cardiac structures
import numpy as np
from scipy import ndimage
import matplotlib.pyplot as plt

def crop_cardiac_region(params, cardiac_mask, crop_size=(150, 150, 150), debug=True):
    """Crop all parameter maps to focus on the cardiac region with improved LV centering"""
    # Find cardiac tissues in the Labels volume
    labels = params['Labels']
    print(f"Labels volume shape: {labels.shape}")
    
    # Create mask for cardiac structures (LV wall=1, RV wall=2, LV blood=5, RV blood=6)
    # cardiac_mask = np.zeros_like(labels, dtype=bool)
    # cardiac_mask = np.load('/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/output/male_169/male_169_cardiac_box_mask_20250610_211706.npy')
    lv_wall_mask = (labels == 1)
    lv_blood_mask = (labels == 5)
    rv_wall_mask = (labels == 2)
    rv_blood_mask = (labels == 6)
    
    # Combined masks
    # cardiac_mask =  rv_blood_mask 
    print(f"Size of caridac_mask: {cardiac_mask.shape}")
    lv_mask = cardiac_mask
    
    # Check if masks contain any voxels
    print(f"LV wall voxels: {np.sum(lv_wall_mask)}")
    print(f"LV blood voxels: {np.sum(lv_blood_mask)}")
    print(f"Total cardiac voxels: {np.sum(cardiac_mask)}")
    
    # Find center with priority on LV wall
    if np.sum(lv_mask) > 20:
        center = ndimage.center_of_mass(lv_mask)
        center = np.round(center).astype(int)
        print(f"Found LV center at {center}")
    elif np.sum(cardiac_mask) > 20:
        center = ndimage.center_of_mass(cardiac_mask)
        center = np.round(center).astype(int)
        print(f"Found cardiac center at {center}")
    else:
        center = np.array(labels.shape) // 2
        print(f"No cardiac structures found. Using volume center {center}")
    
    # Visualize the detected center and masks if debugging
    if debug:
        # Create central orthogonal slices through the detected center
        x_slice, y_slice, z_slice = center
        
        plt.figure(figsize=(16, 12))
        
        # Axial view with center marked
        plt.subplot(231)
        plt.imshow(labels[:, :, z_slice])
        plt.plot(y_slice, x_slice, 'r+', markersize=12)  # Mark center
        plt.title(f"Labels - Axial (z={z_slice})")
        
        # Coronal view with center marked
        plt.subplot(232)
        plt.imshow(labels[:, y_slice, :])
        plt.plot(z_slice, x_slice, 'r+', markersize=12)  # Mark center
        plt.title(f"Labels - Coronal (y={y_slice})")
        
        # Sagittal view with center marked
        plt.subplot(233)
        plt.imshow(labels[x_slice, :, :])
        plt.plot(z_slice, y_slice, 'r+', markersize=12)  # Mark center
        plt.title(f"Labels - Sagittal (x={x_slice})")
        
        # Visualize masks
        plt.subplot(234)
        plt.imshow(lv_wall_mask[:, :, z_slice], cmap='Reds')
        plt.title("LV Wall Mask")
        
        plt.subplot(235)
        plt.imshow(lv_blood_mask[:, :, z_slice], cmap='Blues')
        plt.title("LV Blood Mask")
        
        plt.subplot(236)
        overlay = np.zeros((labels.shape[0], labels.shape[1], 3))
        overlay[:,:,0] = lv_wall_mask[:, :, z_slice]  # Red = LV wall
        overlay[:,:,2] = lv_blood_mask[:, :, z_slice]  # Blue = LV blood
        overlay[:,:,1] = rv_wall_mask[:, :, z_slice] | rv_blood_mask[:, :, z_slice]  # Green = RV
        plt.imshow(overlay)
        plt.plot(y_slice, x_slice, 'y+', markersize=12)  # Mark center
        plt.title("Combined Cardiac Masks")
        
        plt.tight_layout()
        plt.show()
    
    # Calculate crop boundaries
    half_size = np.array(crop_size) // 2
    min_bounds = center - half_size
    max_bounds = center + half_size
    
    # Store original bounds for debugging
    original_bounds = (min_bounds.copy(), max_bounds.copy())
    
    # Ensure bounds are within volume dimensions, maintaining the center as much as possible
    vol_shape = np.array(labels.shape)
    for i in range(3):
        # Check lower bound
        if min_bounds[i] < 0:
            # Shift both bounds while keeping crop size constant
            shift = -min_bounds[i]
            min_bounds[i] = 0
            max_bounds[i] += shift
            
            # Check if the shift pushed the upper bound past the edge
            if max_bounds[i] > vol_shape[i]:
                # If so, clamp and recenter as best as possible
                max_bounds[i] = vol_shape[i]
                # Recalculate min_bound to maintain crop size if possible
                desired_size = crop_size[i]
                min_bounds[i] = max(0, max_bounds[i] - desired_size)
        
        # Check upper bound
        elif max_bounds[i] > vol_shape[i]:
            # Shift both bounds while keeping crop size constant
            shift = max_bounds[i] - vol_shape[i]
            max_bounds[i] = vol_shape[i]
            min_bounds[i] = max(0, min_bounds[i] - shift)
    
    # Print actual crop dimensions for debugging
    if debug:
        actual_size = max_bounds - min_bounds
        print(f"Actual crop dimensions: {actual_size}")
        print(f"Original bounds: {original_bounds}")
        print(f"Adjusted bounds: {(min_bounds, max_bounds)}")
        
        # Check how far the center shifted
        original_center = (original_bounds[0] + original_bounds[1]) / 2
        adjusted_center = (min_bounds + max_bounds) / 2
        shift = adjusted_center - original_center
        print(f"Center shifted by: {shift} ({np.linalg.norm(shift):.1f} units)")
    
    # Crop all parameter maps
    cropped = {}
    for name, volume in params.items():
        x_min, y_min, z_min = min_bounds
        x_max, y_max, z_max = max_bounds
        
        # Crop and store
        cropped[name] = volume[x_min:x_max, y_min:y_max, z_min:z_max]
        print(f"Cropped {name}: {volume.shape} → {cropped[name].shape}")
    
    # Add crop information for visualization and future use
    cropped['_center'] = center
    cropped['_bounds'] = (min_bounds, max_bounds)
    cropped['_original_bounds'] = original_bounds
    
    # Visualize final crop if debugging
    if debug:
        plt.figure(figsize=(16, 5))
        
        # Show original with crop outline
        plt.subplot(131)
        plt.imshow(labels[:,:,z_slice])
        plt.plot(y_slice, x_slice, 'r+', markersize=12)  # Mark center
        
        # Add crop outline
        x_min, y_min, _ = min_bounds
        x_max, y_max, _ = max_bounds
        plt.plot([y_min, y_max, y_max, y_min, y_min], 
                [x_min, x_min, x_max, x_max, x_min], 'r-', linewidth=2)
        plt.title("Original with Crop Outline")
        
        # Show cropped volume - labels
        plt.subplot(132)
        plt.imshow(cropped['Labels'][:,:,cropped['Labels'].shape[2]//2])
        plt.title("Cropped Labels")
        
        # Show cropped volume - T2*
        plt.subplot(133)
        plt.imshow(cropped['T2*+'][:,:,cropped['T2*+'].shape[2]//2], cmap='inferno')
        plt.colorbar(label='T2* (ms)')
        plt.title("Cropped T2*+")
        
        plt.tight_layout()
        plt.show()
    
    return cropped

# Modified crop_b0_field function for pre-aligned B0 field
def crop_b0_field_aligned(b0_field, crop_bounds):
    """Crop the pre-aligned B0 field to match the cardiac region bounds"""
    min_bounds, max_bounds = crop_bounds
    x_min, y_min, z_min = min_bounds
    x_max, y_max, z_max = max_bounds
    
    # Get current shape
    current_shape = b0_field.shape
    print(f"Pre-aligned B0 field shape: {current_shape}")
    
    # Verify shape matches other parameter maps (500, 500, 269)
    expected_shape = (500, 500, 269)
    if current_shape != expected_shape:
        print(f"Warning: B0 field shape {current_shape} differs from expected {expected_shape}")
        print("This may cause alignment issues. Continuing anyway...")
    
    # Check boundaries are valid
    if z_max <= b0_field.shape[2] and y_max <= b0_field.shape[1] and x_max <= b0_field.shape[0]:
        # Crop using the same bounds as other parameter maps
        cropped_b0 = b0_field[x_min:x_max, y_min:y_max, z_min:z_max]
        print(f"Cropped B0 field: {b0_field.shape} → {cropped_b0.shape}")
        
        # Visual debug for B0 field orientation - show middle slice
        plt.figure(figsize=(12, 4))
        
        # Show original B0 middle slice
        plt.subplot(131)
        mid_z = b0_field.shape[2] // 2
        plt.imshow(b0_field[:, :, mid_z], cmap='coolwarm')
        plt.title(f"Original aligned B0 (z={mid_z})")
        plt.colorbar()
        
        # Show cropped region boundaries on B0
        plt.subplot(132)
        crop_z = min(mid_z, z_min + (z_max-z_min)//2)
        plt.imshow(b0_field[:, :, crop_z], cmap='coolwarm')
        plt.title(f"B0 with crop region (z={crop_z})")
        plt.axvline(x=y_min, color='r')
        plt.axvline(x=y_max, color='r')
        plt.axhline(y=x_min, color='r')
        plt.axhline(y=x_max, color='r')
        plt.colorbar()
        
        # Show cropped B0
        plt.subplot(133)
        plt.imshow(cropped_b0[:, :, cropped_b0.shape[2]//2], cmap='coolwarm')
        plt.title("Cropped B0")
        plt.colorbar()
        
        plt.tight_layout()
        plt.show()
        
        return cropped_b0
    else:
        print(f"Error: Crop bounds {min_bounds}-{max_bounds} exceed B0 dimensions {b0_field.shape}")
        return None

# First crop the parameter maps
cardiac_params = crop_cardiac_region(params, cardiac_mask[:, :, z_min:z_max], crop_size=(150, 150, 150), debug=True)

# Create individual variables for each cropped parameter
PD_cardiac = cardiac_params['PD']
T1_cardiac = cardiac_params['T1']
T2_cardiac = cardiac_params['T2'] 
T2star_cardiac = cardiac_params['T2*']
T2star_plus_cardiac = cardiac_params['T2*+']
Labels_cardiac = cardiac_params['Labels']

# Save crop bounds for later use
crop_bounds = cardiac_params['_bounds']

b0_cardiac = crop_b0_field_aligned(b0_heart_resized, crop_bounds)


# Add B0 to cardiac params dictionary
# 替换原来的问题代码行
if b0_cardiac is not None:
    # 使用LV wall作为参考区域
    lv_wall_cropped = (cardiac_params['Labels'] == 1)
    
    if np.sum(lv_wall_cropped) > 0:
        cardiac_params['B0'] = b0_cardiac - np.median(b0_cardiac[lv_wall_cropped])
    else:
        # 备选：使用所有心脏组织
        tissue_mask = cardiac_params['Labels'] > 0
        cardiac_params['B0'] = b0_cardiac - np.median(b0_cardiac[tissue_mask])
    
    print("Added B0 field to cardiac_params dictionary")


# Visualize alignment between cardiac anatomy and B0 field
if b0_cardiac is not None:
    plt.figure(figsize=(15, 5))
    
    # Show middle slice of cardiac labels
    plt.subplot(131)
    z_mid = cardiac_params['Labels'].shape[2] // 2
    plt.imshow(cardiac_params['Labels'][:,:,z_mid], cmap='tab10', alpha=0.8)
    plt.title("Cardiac labels")
    plt.axis('off')
    
    # Show middle slice of B0 field
    plt.subplot(132)
    plt.imshow(b0_cardiac[:,:,z_mid], cmap='coolwarm')
    plt.title("B0 field")
    plt.colorbar(label='Field (Hz)')
    plt.axis('off')
    
    # Show overlay
    plt.subplot(133)
    plt.imshow(b0_cardiac[:,:,z_mid], cmap='coolwarm', alpha=0.7)
    plt.imshow(cardiac_params['Labels'][:,:,z_mid], cmap='gray', alpha=0.3)
    plt.title("B0 + anatomy overlay")
    plt.colorbar(label='Field (Hz)')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()

# Visualize alignment between cardiac anatomy and B0 field
if b0_cardiac is not None:
    plt.figure(figsize=(15, 5))
    
    # Show middle slice of cardiac labels
    plt.subplot(131)
    z_mid = cardiac_params['Labels'].shape[2] // 2
    plt.imshow(cardiac_params['Labels'][:,:,z_mid], cmap='tab10', alpha=0.8)
    plt.title("Cardiac labels")
    plt.axis('off')
    
    # Show middle slice of B0 field
    plt.subplot(132)
    plt.imshow(b0_cardiac[:,:,z_mid], cmap='coolwarm')
    plt.title("B0 field")
    plt.colorbar(label='Field (Hz)')
    plt.axis('off')
    
    # Show overlay
    plt.subplot(133)
    plt.imshow(b0_cardiac[:,:,z_mid], cmap='coolwarm', alpha=0.7)
    plt.imshow(cardiac_params['Labels'][:,:,z_mid], cmap='gray', alpha=0.3)
    plt.title("B0 + anatomy overlay")
    plt.colorbar(label='Field (Hz)')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()

# Save all parameters in a convenient dictionary
cardiac_volume = {
    'PD': PD_cardiac,
    'T1': T1_cardiac,
    'T2': T2_cardiac,
    'T2star': T2star_cardiac,
    'T2star_plus': T2star_plus_cardiac,
    'Labels': Labels_cardiac
}

# Visualize the cropped region
plt.figure(figsize=(15, 5))

# Show original with crop overlay
center_slice = params['Labels'].shape[2] // 2
plt.subplot(131)
plt.imshow(params['Labels'][:,:,center_slice])

# Add crop outline
min_bounds, max_bounds = cardiac_params['_bounds']
x_min, y_min, _ = min_bounds
x_max, y_max, _ = max_bounds
plt.plot([y_min, y_max, y_max, y_min, y_min], 
         [x_min, x_min, x_max, x_max, x_min], 'r-', linewidth=2)
plt.title("Original with Crop Region")

# Show cropped volume
plt.subplot(132)
crop_center = cardiac_params['Labels'].shape[2] // 2
plt.imshow(cardiac_params['Labels'][:,:,crop_center])
plt.title("Cropped Cardiac Region")

# Show parameter map
plt.subplot(133)
plt.imshow(cardiac_params['T2*+'][:,:,crop_center], cmap='inferno')
plt.colorbar(label='T2* (ms)')
plt.title("T2*+ Parameter Map (Cropped)")

plt.tight_layout()
plt.show()

print("\nCropped cardiac-centered parameter maps created:")
for name, arr in cardiac_params.items():
    if not name.startswith('_'):
        print(f"  {name}_cardiac: shape {arr.shape}")
        
# Visualize the cropped region with all parameters including B0
plt.figure(figsize=(20, 10))

# Get middle slice for each dimension
z_slice = cardiac_params['Labels'].shape[2] // 2
y_slice = cardiac_params['Labels'].shape[1] // 2
x_slice = cardiac_params['Labels'].shape[0] // 2

# Show XY plane (axial)
plt.subplot(2, 3, 1)
plt.imshow(cardiac_params['Labels'][:, :, z_slice])
plt.title(f"Labels - Axial (z={z_slice})")

# Show XZ plane (coronal)
plt.subplot(2, 3, 2)
plt.imshow(cardiac_params['Labels'][:, y_slice, :])
plt.title(f"Labels - Coronal (y={y_slice})")

# Show YZ plane (sagittal)
plt.subplot(2, 3, 3)
plt.imshow(cardiac_params['Labels'][x_slice, :, :])
plt.title(f"Labels - Sagittal (x={x_slice})")

# Show B0 field if available
if 'B0' in cardiac_params:
    # Show XY plane (axial)
    plt.subplot(2, 3, 4)
    plt.imshow(cardiac_params['B0'][:, :, z_slice])
    plt.title(f"B0 Field - Axial (z={z_slice})")
    plt.colorbar()

    # Show XZ plane (coronal)
    plt.subplot(2, 3, 5)
    plt.imshow(cardiac_params['B0'][:, y_slice, :])
    plt.title(f"B0 Field - Coronal (y={y_slice})")
    plt.colorbar()

    # Show YZ plane (sagittal)
    plt.subplot(2, 3, 6)
    plt.imshow(cardiac_params['B0'][x_slice, :, :])
    plt.title(f"B0 Field - Sagittal (x={x_slice})")
    plt.colorbar()

plt.tight_layout()
plt.show()

# 1. Get the shape of the shimmed B0 map before any transformations
print(f"Original shimmed B0 map shape: {shimmed_b0_map.shape}")

# 2. Transpose to align with T2* volume (same as original B0 map)
shimmed_b0_map_aligned = np.transpose(shimmed_b0_map, [1,0,2])
print(f"Transposed shimmed B0 map shape: {shimmed_b0_map_aligned.shape}")

# 3. Extract the relevant portion that corresponds to the heart region
# Use the same slice range as was used for the original B0 map
b0_start_slice = z_min
b0_end_slice = z_max
print(f"Extracting shimmed B0 map slices {b0_start_slice} to {b0_end_slice}")
shimmed_b0_heart = shimmed_b0_map_aligned[:, :, b0_start_slice:b0_end_slice+1]
print(f"Extracted shimmed B0 map shape: {shimmed_b0_heart.shape}")

slice_number = z_max - z_min + 1
# 4. Check if resizing is needed to match T2* volume dimensions
t2s_shape = (500, 500, slice_number)  # Expected T2* volume shape
if shimmed_b0_heart.shape != t2s_shape:
    print(f"Resizing shimmed B0 map to match T2* volume dimensions...")
    zoom_factors = [
        t2s_shape[0] / shimmed_b0_heart.shape[0],
        t2s_shape[1] / shimmed_b0_heart.shape[1],
        t2s_shape[2] / shimmed_b0_heart.shape[2]
    ]
    shimmed_b0_heart_resized = zoom(shimmed_b0_heart, zoom_factors, order=1)
    print(f"Resized shimmed B0 map shape: {shimmed_b0_heart_resized.shape}")
else:
    shimmed_b0_heart_resized = shimmed_b0_heart.copy()  # Make a copy to avoid modifying original

# 5. Apply transpose to each slice to match orientations
# FIXED: Create a new array instead of modifying in-place
print("Applying orientation adjustments to shimmed B0 map...")
shimmed_b0_oriented = np.zeros_like(shimmed_b0_heart_resized)
for z in range(shimmed_b0_heart_resized.shape[2]):
    shimmed_b0_oriented[:, :, z] = np.transpose(shimmed_b0_heart_resized[:, :, z]) / 2

# 6. Now crop to match the cardiac region
shimmed_b0_cardiac = crop_b0_field_aligned(shimmed_b0_oriented, crop_bounds)
shimmed_b0_cardiac_save = shimmed_b0_cardiac.copy()  # Save a copy

# 7. Add the shimmed B0 map to cardiac params and volume dictionaries
if shimmed_b0_cardiac is not None:
    cardiac_params['ShimmedB0'] = shimmed_b0_cardiac
    print("Added shimmed B0 field to cardiac_params and cardiac_volume dictionaries")
    print(f"Shimmed B0 cardiac shape: {shimmed_b0_cardiac.shape}")
    
    # Check if shapes match
    if 'B0' in cardiac_params:
        print(f"Original B0 cardiac shape: {cardiac_params['B0'].shape}")
        if cardiac_params['ShimmedB0'].shape != cardiac_params['B0'].shape:
            print("WARNING: Shape mismatch between original and shimmed B0 fields!")
        else:
            print("Shape match confirmed between original and shimmed B0 fields")

# ======= Visualization of original and shimmed B0 fields =======
# First ensure the shimmed B0 field is added to cardiac_params correctly
if 'ShimmedB0' in cardiac_params and 'B0' in cardiac_params:
    print(f"Original B0 shape: {cardiac_params['B0'].shape}")
    print(f"Shimmed B0 shape: {cardiac_params['ShimmedB0'].shape}")
    
    # Visualize B0 fields in all 3 planes
    plt.figure(figsize=(20, 15))
    
    # Get middle slice for each dimension
    z_slice = cardiac_params['Labels'].shape[2] // 2
    y_slice = cardiac_params['Labels'].shape[1] // 2
    x_slice = cardiac_params['Labels'].shape[0] // 2
    
    # Common colormap and range for all B0 visualizations
    b0_cmap = 'jet'
    b0_range = (-200, 200)  # Hz
    
    # ===== Axial view (XY plane) =====
    # Original B0
    plt.subplot(3, 3, 1)
    plt.imshow(cardiac_params['B0'][:, :, z_slice], cmap=b0_cmap, vmin=b0_range[0], vmax=b0_range[1])
    plt.title(f"Original B0 - Axial (z={z_slice})")
    plt.colorbar(label='Field (Hz)')
    
    # Shimmed B0
    plt.subplot(3, 3, 2)
    plt.imshow(cardiac_params['ShimmedB0'][:, :, z_slice], cmap=b0_cmap, vmin=b0_range[0], vmax=b0_range[1])
    plt.title(f"Shimmed B0 - Axial (z={z_slice})")
    plt.colorbar(label='Field (Hz)')
    
    # Difference (Original - Shimmed)
    plt.subplot(3, 3, 3)
    diff_axial = cardiac_params['B0'][:, :, z_slice] - cardiac_params['ShimmedB0'][:, :, z_slice]
    plt.imshow(diff_axial, cmap='coolwarm', vmin=-100, vmax=100)
    plt.title(f"Difference - Axial (z={z_slice})")
    plt.colorbar(label='Field Difference (Hz)')
    
    # ===== Coronal view (XZ plane) =====
    # Original B0
    plt.subplot(3, 3, 4)
    plt.imshow(cardiac_params['B0'][:, y_slice, :], cmap=b0_cmap, vmin=b0_range[0], vmax=b0_range[1])
    plt.title(f"Original B0 - Coronal (y={y_slice})")
    plt.colorbar(label='Field (Hz)')
    
    # Shimmed B0
    plt.subplot(3, 3, 5)
    plt.imshow(cardiac_params['ShimmedB0'][:, y_slice, :], cmap=b0_cmap, vmin=b0_range[0], vmax=b0_range[1])
    plt.title(f"Shimmed B0 - Coronal (y={y_slice})")
    plt.colorbar(label='Field (Hz)')
    
    # Difference (Original - Shimmed)
    plt.subplot(3, 3, 6)
    diff_coronal = cardiac_params['B0'][:, y_slice, :] - cardiac_params['ShimmedB0'][:, y_slice, :]
    plt.imshow(diff_coronal, cmap='coolwarm', vmin=-100, vmax=100)
    plt.title(f"Difference - Coronal (y={y_slice})")
    plt.colorbar(label='Field Difference (Hz)')
    
    # ===== Sagittal view (YZ plane) =====
    # Original B0
    plt.subplot(3, 3, 7)
    plt.imshow(cardiac_params['B0'][x_slice, :, :], cmap=b0_cmap, vmin=b0_range[0], vmax=b0_range[1])
    plt.title(f"Original B0 - Sagittal (x={x_slice})")
    plt.colorbar(label='Field (Hz)')
    
    # Shimmed B0
    plt.subplot(3, 3, 8)
    plt.imshow(cardiac_params['ShimmedB0'][x_slice, :, :], cmap=b0_cmap, vmin=b0_range[0], vmax=b0_range[1])
    plt.title(f"Shimmed B0 - Sagittal (x={x_slice})")
    plt.colorbar(label='Field (Hz)')
    
    # Difference (Original - Shimmed)
    plt.subplot(3, 3, 9)
    diff_sagittal = cardiac_params['B0'][x_slice, :, :] - cardiac_params['ShimmedB0'][x_slice, :, :]
    plt.imshow(diff_sagittal, cmap='coolwarm', vmin=-100, vmax=100)
    plt.title(f"Difference - Sagittal (x={x_slice})")
    plt.colorbar(label='Field Difference (Hz)')
    
    plt.tight_layout()
    plt.suptitle("Comparison of Original and Shimmed B0 Fields", fontsize=16)
    plt.subplots_adjust(top=0.93)
    plt.show()
    
    # ===== Histogram comparison =====
    plt.figure(figsize=(20, 6))
    
    
    cardiac_mask_crop = (cardiac_params['Labels'] == 1) | \
                    (cardiac_params['Labels'] == 2) | \
                    (cardiac_params['Labels'] == 5) | \
                    (cardiac_params['Labels'] == 6)
    
    print(f'cardiac mask shape crop {np.shape(cardiac_mask_crop)}')
    # Extract field values inside cardiac tissues
    original_b0_cardiac = cardiac_params['B0'][cardiac_mask_crop]
    shimmed_b0_cardiac = cardiac_params['ShimmedB0'][cardiac_mask_crop]
    
    # Calculate statistics
    orig_mean = np.mean(original_b0_cardiac)
    orig_std = np.std(original_b0_cardiac)
    orig_rms = np.sqrt(np.mean((original_b0_cardiac - orig_mean)**2))
    
    shim_mean = np.mean(shimmed_b0_cardiac)
    shim_std = np.std(shimmed_b0_cardiac)
    shim_rms = np.sqrt(np.mean((shimmed_b0_cardiac - shim_mean)**2))
    
    improvement = (1 - shim_rms/orig_rms) * 100
    
    # Histogram of original B0 field in cardiac region
    plt.subplot(131)
    plt.hist(original_b0_cardiac, bins=50, alpha=0.7, color='blue')
    plt.title(f'Original B0 Histogram\nMean: {orig_mean:.2f} Hz, RMS: {orig_rms:.2f} Hz')
    plt.xlabel('Field (Hz)')
    plt.ylabel('Voxel Count')
    plt.axvline(x=orig_mean, color='red', linestyle='--', linewidth=2)
    
    # Histogram of shimmed B0 field in cardiac region
    plt.subplot(132)
    plt.hist(shimmed_b0_cardiac, bins=50, alpha=0.7, color='green')
    plt.title(f'Shimmed B0 Histogram\nMean: {shim_mean:.2f} Hz, RMS: {shim_rms:.2f} Hz')
    plt.xlabel('Field (Hz)')
    plt.ylabel('Voxel Count')
    plt.axvline(x=shim_mean, color='red', linestyle='--', linewidth=2)
    
    # Overlay of both histograms
    plt.subplot(133)
    plt.hist(original_b0_cardiac, bins=50, alpha=0.5, color='blue', label='Original')
    plt.hist(shimmed_b0_cardiac, bins=50, alpha=0.5, color='green', label='Shimmed')
    plt.title(f'Improvement: {improvement:.1f}%')
    plt.xlabel('Field (Hz)')
    plt.ylabel('Voxel Count')
    plt.legend()
    
    plt.tight_layout()
    plt.suptitle("B0 Field Distribution in Cardiac Region", fontsize=16)
    plt.subplots_adjust(top=0.85)
    plt.show()
    
    # ===== B0 Field Overlay with Cardiac Anatomy =====
    plt.figure(figsize=(20, 6))
    
    # Get middle slice
    z_mid = cardiac_params['Labels'].shape[2] // 2
    
    # Create binary mask of cardiac tissue for this slice
    cardiac_slice = (cardiac_params['Labels'][:, :, z_mid] == 1) | \
                    (cardiac_params['Labels'][:, :, z_mid] == 2) | \
                    (cardiac_params['Labels'][:, :, z_mid] == 3) | \
                    (cardiac_params['Labels'][:, :, z_mid] == 4) | \
                    (cardiac_params['Labels'][:, :, z_mid] == 5) | \
                    (cardiac_params['Labels'][:, :, z_mid] == 6) | \
                    (cardiac_params['Labels'][:, :, z_mid] == 7) | \
                    (cardiac_params['Labels'][:, :, z_mid] == 8)
                    # (cardiac_params['Labels'][:, :, z_mid] == 58) 
                        
    
    # Create outline of cardiac mask
    from skimage import measure
    contours = measure.find_contours(cardiac_slice.astype(float), 0.5)
    
    # Original B0 with cardiac outline
    plt.subplot(131)
    plt.imshow(cardiac_params['Labels'][:, :, z_mid], cmap='gray', alpha=0.3)
    plt.imshow(cardiac_params['B0'][:, :, z_mid], cmap='jet', vmin=-200, vmax=200, alpha=0.7)
    for contour in contours:
        plt.plot(contour[:, 1], contour[:, 0], 'r-', linewidth=2)
    plt.title("Original B0 + Cardiac Anatomy")
    plt.colorbar(label='Field (Hz)')
    plt.axis('off')
    
    # Shimmed B0 with cardiac outline
    plt.subplot(132)
    plt.imshow(cardiac_params['Labels'][:, :, z_mid], cmap='gray', alpha=0.3)
    plt.imshow(cardiac_params['ShimmedB0'][:, :, z_mid], cmap='jet', vmin=-200, vmax=200, alpha=0.7)
    for contour in contours:
        plt.plot(contour[:, 1], contour[:, 0], 'r-', linewidth=2)
    plt.title("Shimmed B0 + Cardiac Anatomy")
    plt.colorbar(label='Field (Hz)')
    plt.axis('off')
    
    # Difference map with cardiac outline
    plt.subplot(133)
    diff_map = cardiac_params['B0'][:, :, z_mid] - cardiac_params['ShimmedB0'][:, :, z_mid]
    plt.imshow(cardiac_params['Labels'][:, :, z_mid], cmap='gray', alpha=0.3)
    plt.imshow(diff_map, cmap='coolwarm', vmin=-100, vmax=100, alpha=0.7)
    for contour in contours:
        plt.plot(contour[:, 1], contour[:, 0], 'k-', linewidth=2)
    plt.title("Difference Map + Cardiac Anatomy")
    plt.colorbar(label='Field Difference (Hz)')
    plt.axis('off')
    
    plt.tight_layout()
    plt.suptitle("B0 Field Overlay with Cardiac Anatomy", fontsize=16)
    plt.subplots_adjust(top=0.85)
    plt.show()
    
else:
    print("Error: Both original and shimmed B0 fields must be available for visualization")

# Save cropped cardiac parameter maps to NIFTI files
import os
import numpy as np
import datetime

# Define output directory
# input_vti = '/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/male169/male_169.samp_act_fixed.vti'
# results_folder
output_dir = os.path.join(os.path.dirname(output_folder_bin), 'cardiac_region_new')
if not os.path.exists(output_dir):
    os.makedirs(output_dir)
    print(f"Created directory: {output_dir}")

# Try to save as NIFTI files (preferred for medical imaging)
try:
    import nibabel as nib
    
    # Create affine matrix with proper voxel dimensions
    # Using standard 1mm isotropic voxels, or adjust if you know actual dimensions
    voxel_size = 2.0  # mm
    affine = np.diag([voxel_size, voxel_size, voxel_size, 1])
    
    # List of parameters to save
    params_to_save = [
        ('PD', PD_cardiac),
        ('T1', T1_cardiac), 
        ('T2', T2_cardiac),
        ('T2star', T2star_cardiac),
        ('T2star_plus', T2star_plus_cardiac),
        ('Labels', Labels_cardiac.astype(np.uint8)),  # Labels as integer type
        ('B0', b0_cardiac),  # Explicitly include B0
        ('ShimmedB0', shimmed_b0_cardiac_save),
    ]
    
    # Save each parameter
    saved_files = []
    for name, data in params_to_save:
        # Skip if data is None
        if data is None:
            print(f"Skipping {name} - data is None")
            continue
            
        # Create appropriate file name
        file_path = os.path.join(output_dir, f"{name}_cardiac.nii.gz")
        
        # Create and save NIFTI image
        nii_img = nib.Nifti1Image(data, affine)
        
        # Add descriptions
        nii_img.header['descrip'] = f'MRXCAT {name} parameter map'
        
        # Save the file
        nib.save(nii_img, file_path)
        saved_files.append(file_path)
        
        print(f"Saved {name} NIFTI file: {os.path.basename(file_path)}")
    
    print(f"\nSaved {len(saved_files)} NIFTI files to {output_dir}")

except ImportError:
    print("\nNibabel library not found. Saving as NumPy arrays instead.")
    
    # Save as NumPy arrays as fallback
    for name, data in [
        ('PD', PD_cardiac),
        ('T1', T1_cardiac), 
        ('T2', T2_cardiac),
        ('T2star', T2star_cardiac),
        ('T2star_plus', T2star_plus_cardiac),
        ('Labels', Labels_cardiac),
        ('B0', b0_cardiac),  # Explicitly include B0
        ('ShimmedB0', shimmed_b0_cardiac_save),
    ]:
        if data is None:
            print(f"Skipping {name} - data is None")
            continue
            
        file_path = os.path.join(output_dir, f"{name}_cardiac.npy")
        np.save(file_path, data)
        print(f"Saved {name} to {file_path}")
    
    print(f"\nTo install nibabel for better medical image support, run: pip install nibabel")

# Also save the cardiac volume dictionary for easy reloading
cardiac_dict_path = os.path.join(output_dir, "cardiac_volume.npz")

# Create a dictionary with all parameters
save_dict = {
    'PD': PD_cardiac,
    'T1': T1_cardiac,
    'T2': T2_cardiac,
    'T2star': T2star_cardiac,
    'T2star_plus': T2star_plus_cardiac,
    'Labels': Labels_cardiac
}

# Add B0 if it exists
if b0_cardiac is not None:
    save_dict['B0'] = b0_cardiac
if shimmed_b0_cardiac_save is not None:
    save_dict['ShimmedB0'] = shimmed_b0_cardiac_save

# Save as compressed NPZ file
np.savez_compressed(cardiac_dict_path, **save_dict)
print(f"Saved all parameters to single file: {cardiac_dict_path}")

# Save some metadata about the cropping
metadata_path = os.path.join(output_dir, "cardiac_metadata.txt")
with open(metadata_path, 'w') as f:
    f.write(f"Original volume shape: {params['Labels'].shape}\n")
    f.write(f"Cardiac center: {cardiac_params['_center']}\n")
    f.write(f"Crop bounds: {cardiac_params['_bounds']}\n")
    f.write(f"Cropped volume shape: {Labels_cardiac.shape}\n")
    if b0_cardiac is not None:
        f.write(f"B0 field shape: {b0_cardiac.shape}\n")
    if shimmed_b0_cardiac_save is not None:
        f.write(f"Shimmed B0 field shape: {shimmed_b0_cardiac_save.shape}\n")
    f.write(f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

print(f"Saved metadata to: {metadata_path}")

# Print summary of all saved files
print("\nSummary of saved cardiac parameter maps:")
print(f"{'Parameter':<12} {'Shape':<15} {'Type':<12} {'Range':<20}")
print("-" * 60)
for name, data in [
    ('PD', PD_cardiac),
    ('T1', T1_cardiac),
    ('T2', T2_cardiac),
    ('T2*', T2star_cardiac),
    ('T2*+', T2star_plus_cardiac),
    ('Labels', Labels_cardiac),
    ('B0', b0_cardiac),
    ('ShimmedB0', shimmed_b0_cardiac_save),
]:
    if data is not None:
        min_val = np.min(data)
        max_val = np.max(data)
        print(f"{name:<12} {str(data.shape):<15} {data.dtype!s:<12} [{min_val:.2f}, {max_val:.2f}]")