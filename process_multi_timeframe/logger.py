#!/usr/bin/env python3
"""
Logging functionality for XCAT processing pipeline
Provides comprehensive logging with file output and console display
"""

import os
import sys
import logging
import datetime
from pathlib import Path
from contextlib import redirect_stdout, redirect_stderr
import io

class TeeStream:
    """Stream that writes to both original stream and log file"""
    def __init__(self, original_stream, log_file):
        self.original_stream = original_stream
        self.log_file = log_file

    def write(self, data):
        # Write to original stream (console)
        self.original_stream.write(data)
        self.original_stream.flush()

        # Write to log file
        if self.log_file and not self.log_file.closed:
            self.log_file.write(data)
            self.log_file.flush()

    def flush(self):
        self.original_stream.flush()
        if self.log_file and not self.log_file.closed:
            self.log_file.flush()

    def __getattr__(self, name):
        return getattr(self.original_stream, name)

class XCATLogger:
    """Custom logger for XCAT processing pipeline"""
    
    def __init__(self, log_dir="logs", log_level=logging.INFO):
        """
        Initialize logger

        Parameters:
        -----------
        log_dir : str
            Directory to store log files
        log_level : int
            Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)

        # Create timestamp for this session
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        # Setup logger
        self.logger = logging.getLogger('XCAT_Pipeline')
        self.logger.setLevel(log_level)

        # Clear any existing handlers
        self.logger.handlers.clear()

        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_formatter = logging.Formatter(
            '%(levelname)s: %(message)s'
        )

        # File handler for main log
        main_log_file = self.log_dir / f"xcat_pipeline_{self.timestamp}.log"
        file_handler = logging.FileHandler(main_log_file)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

        # Store file paths for reference
        self.main_log_file = main_log_file
        self.error_log_file = self.log_dir / f"xcat_errors_{self.timestamp}.log"

        # Create error-only handler
        error_handler = logging.FileHandler(self.error_log_file)
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        self.logger.addHandler(error_handler)

        # Setup stdout/stderr capture to also log all print statements
        self.setup_stdout_capture()

        self.info(f"XCAT Pipeline Logger initialized")
        self.info(f"Main log file: {self.main_log_file}")
        self.info(f"Error log file: {self.error_log_file}")
        self.info(f"All print statements will be captured in log files")

    def setup_stdout_capture(self):
        """Setup stdout/stderr capture to log all print statements"""
        # Create a combined log file for all output (print statements + logger messages)
        self.combined_log_file = self.log_dir / f"xcat_complete_{self.timestamp}.log"
        self.combined_log_handle = open(self.combined_log_file, 'w', encoding='utf-8')

        # Store original streams
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr

        # Replace stdout and stderr with tee streams
        sys.stdout = TeeStream(self.original_stdout, self.combined_log_handle)
        sys.stderr = TeeStream(self.original_stderr, self.combined_log_handle)

        # Write header to combined log
        header = f"XCAT Pipeline Complete Log - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        header += "=" * 80 + "\n"
        header += "This log contains ALL output including print statements and logger messages\n"
        header += "=" * 80 + "\n\n"
        self.combined_log_handle.write(header)
        self.combined_log_handle.flush()

    def restore_stdout(self):
        """Restore original stdout/stderr"""
        if hasattr(self, 'original_stdout'):
            sys.stdout = self.original_stdout
        if hasattr(self, 'original_stderr'):
            sys.stderr = self.original_stderr
        if hasattr(self, 'combined_log_handle') and not self.combined_log_handle.closed:
            self.combined_log_handle.close()

    def __del__(self):
        """Cleanup when logger is destroyed"""
        try:
            self.restore_stdout()
        except:
            pass
    
    def debug(self, message):
        """Log debug message"""
        self.logger.debug(message)
    
    def info(self, message):
        """Log info message"""
        self.logger.info(message)
    
    def warning(self, message):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message):
        """Log error message"""
        self.logger.error(message)
    
    def critical(self, message):
        """Log critical message"""
        self.logger.critical(message)
    
    def log_exception(self, message="Exception occurred"):
        """Log exception with traceback"""
        self.logger.exception(message)
    
    def log_timeframe_start(self, time_frame):
        """Log start of timeframe processing"""
        self.info("=" * 60)
        self.info(f"STARTING TIMEFRAME {time_frame} PROCESSING")
        self.info("=" * 60)
    
    def log_timeframe_end(self, time_frame, success=True):
        """Log end of timeframe processing"""
        status = "COMPLETED" if success else "FAILED"
        self.info("=" * 60)
        self.info(f"TIMEFRAME {time_frame} PROCESSING {status}")
        self.info("=" * 60)
    
    def log_stage_start(self, stage_name):
        """Log start of processing stage"""
        self.info(f"🔄 Starting {stage_name}...")
    
    def log_stage_end(self, stage_name, success=True):
        """Log end of processing stage"""
        status = "✅" if success else "❌"
        self.info(f"{status} {stage_name} {'completed' if success else 'failed'}")
    
    def log_file_operation(self, operation, file_path, success=True):
        """Log file operations"""
        status = "✅" if success else "❌"
        self.info(f"{status} {operation}: {file_path}")
    
    def log_parameter_summary(self, params_dict):
        """Log parameter summary"""
        self.info("Parameter Summary:")
        self.info("-" * 40)
        for name, value in params_dict.items():
            if hasattr(value, 'shape'):
                self.info(f"  {name}: shape={value.shape}, dtype={value.dtype}")
            else:
                self.info(f"  {name}: {value}")
    
    def log_processing_stats(self, stats_dict):
        """Log processing statistics"""
        self.info("Processing Statistics:")
        self.info("-" * 40)
        for key, value in stats_dict.items():
            self.info(f"  {key}: {value}")
    
    def create_timeframe_log(self, time_frame):
        """Create separate log file for specific timeframe"""
        timeframe_log_file = self.log_dir / f"timeframe_{time_frame:02d}_{self.timestamp}.log"
        
        # Create timeframe-specific handler
        timeframe_handler = logging.FileHandler(timeframe_log_file)
        timeframe_handler.setLevel(logging.DEBUG)
        timeframe_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        ))
        
        # Create timeframe logger
        timeframe_logger = logging.getLogger(f'XCAT_Timeframe_{time_frame}')
        timeframe_logger.setLevel(logging.DEBUG)
        timeframe_logger.addHandler(timeframe_handler)
        
        self.info(f"Created timeframe-specific log: {timeframe_log_file}")
        return timeframe_logger, timeframe_log_file
    
    def get_log_summary(self):
        """Get summary of log files created"""
        log_files = list(self.log_dir.glob(f"*{self.timestamp}*"))
        return {
            'main_log': self.main_log_file,
            'error_log': self.error_log_file,
            'complete_log': getattr(self, 'combined_log_file', None),
            'all_logs': log_files,
            'log_directory': self.log_dir
        }

# Global logger instance
_global_logger = None

def get_logger():
    """Get global logger instance"""
    global _global_logger
    if _global_logger is None:
        _global_logger = XCATLogger()
    return _global_logger

def init_logger(log_dir="logs", log_level=logging.INFO):
    """Initialize global logger"""
    global _global_logger
    _global_logger = XCATLogger(log_dir, log_level)
    return _global_logger

# Convenience functions
def log_info(message):
    get_logger().info(message)

def log_error(message):
    get_logger().error(message)

def log_warning(message):
    get_logger().warning(message)

def log_debug(message):
    get_logger().debug(message)

def log_exception(message="Exception occurred"):
    get_logger().log_exception(message)
