#!/usr/bin/env python3
"""
Test shimming functionality with comprehensive logging
"""

import os
import sys
import numpy as np

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from shimming import optimize_shimming, apply_shimming_to_b0_field
from logger import init_logger

def create_realistic_b0_field_and_mask():
    """Create realistic B0 field and cardiac mask for testing"""
    print("Creating realistic B0 field and cardiac mask...")
    
    # Create B0 field with realistic cardiac inhomogeneities
    nx, ny, nz = 150, 150, 150
    
    # Create coordinate grids
    x = np.linspace(-1, 1, nx)
    y = np.linspace(-1, 1, ny)
    z = np.linspace(-1, 1, nz)
    X, Y, Z = np.meshgrid(x, y, z, indexing='ij')
    
    # Create realistic B0 field with multiple sources of inhomogeneity
    b0_field = np.zeros((nx, ny, nz))
    
    # Add main field gradient (simulating imperfect shimming)
    b0_field += 50 * Z  # Linear gradient along z
    b0_field += 30 * (X**2 - Y**2)  # Quadratic terms
    b0_field += 20 * X * Y  # Cross terms
    
    # Add susceptibility effects (air-tissue interfaces)
    # Simulate air cavities causing field distortions
    air_cavity1 = ((X - 0.3)**2 + (Y - 0.2)**2 + (Z - 0.1)**2) < 0.1
    air_cavity2 = ((X + 0.2)**2 + (Y + 0.3)**2 + (Z + 0.2)**2) < 0.08
    
    b0_field[air_cavity1] += -150  # Strong negative field near air
    b0_field[air_cavity2] += -120
    
    # Add some noise
    b0_field += np.random.normal(0, 10, (nx, ny, nz))
    
    # Create cardiac mask (heart-shaped region in center)
    cardiac_mask = np.zeros((nx, ny, nz), dtype=bool)
    
    # Main cardiac region (ellipsoid)
    cardiac_region = ((X/0.4)**2 + (Y/0.3)**2 + (Z/0.5)**2) < 1
    cardiac_mask[cardiac_region] = True
    
    # Remove some regions to make it more realistic
    exclude_region = ((X - 0.1)**2 + (Y - 0.1)**2 + (Z - 0.2)**2) < 0.05
    cardiac_mask[exclude_region] = False
    
    print(f"B0 field shape: {b0_field.shape}")
    print(f"B0 field range: [{np.min(b0_field):.1f}, {np.max(b0_field):.1f}] Hz")
    print(f"Cardiac mask voxels: {np.sum(cardiac_mask)}")
    print(f"B0 field in cardiac region: [{np.min(b0_field[cardiac_mask]):.1f}, {np.max(b0_field[cardiac_mask]):.1f}] Hz")
    
    return b0_field, cardiac_mask

def test_shimming_optimization():
    """Test shimming optimization with detailed logging"""
    print("\n" + "="*60)
    print("TESTING SHIMMING OPTIMIZATION")
    print("="*60)
    
    # Initialize logger
    logger = init_logger(log_dir="logs")
    
    try:
        # Create test data
        b0_field, cardiac_mask = create_realistic_b0_field_and_mask()
        
        # Test shimming optimization
        logger.info("Starting shimming optimization test...")
        
        optimal_coefficients, shim_field, shimmed_b0_map = optimize_shimming(b0_field, cardiac_mask)
        
        logger.info("Shimming optimization test completed successfully")
        
        # Verify results
        print("\nVerification:")
        print(f"✅ Optimal coefficients: {len(optimal_coefficients)} terms")
        print(f"✅ Shim field shape: {shim_field.shape}")
        print(f"✅ Shimmed B0 shape: {shimmed_b0_map.shape}")
        
        # Check if shimming actually improved the field
        original_rms = np.sqrt(np.mean((b0_field[cardiac_mask] - np.mean(b0_field[cardiac_mask]))**2))
        shimmed_rms = np.sqrt(np.mean((shimmed_b0_map[cardiac_mask] - np.mean(shimmed_b0_map[cardiac_mask]))**2))
        
        if shimmed_rms < original_rms:
            print(f"✅ Shimming improved field homogeneity")
        else:
            print(f"⚠️ Shimming did not improve field homogeneity")
        
        return True
        
    except Exception as e:
        logger.error(f"Shimming optimization test failed: {str(e)}")
        logger.log_exception("Shimming test exception details")
        return False

def test_apply_shimming_function():
    """Test the apply_shimming_to_b0_field function"""
    print("\n" + "="*60)
    print("TESTING APPLY_SHIMMING_TO_B0_FIELD FUNCTION")
    print("="*60)
    
    try:
        # Create test data
        b0_field, cardiac_mask = create_realistic_b0_field_and_mask()
        
        # Test the wrapper function
        print("Testing apply_shimming_to_b0_field function...")
        shimmed_b0_map = apply_shimming_to_b0_field(b0_field, cardiac_mask)
        
        if shimmed_b0_map is not None:
            print("✅ apply_shimming_to_b0_field successful")
            print(f"   Input shape: {b0_field.shape}")
            print(f"   Output shape: {shimmed_b0_map.shape}")
            
            # Check improvement
            original_rms = np.sqrt(np.mean((b0_field[cardiac_mask] - np.mean(b0_field[cardiac_mask]))**2))
            shimmed_rms = np.sqrt(np.mean((shimmed_b0_map[cardiac_mask] - np.mean(shimmed_b0_map[cardiac_mask]))**2))
            improvement = (1 - shimmed_rms/original_rms) * 100
            
            print(f"   RMS improvement: {improvement:.1f}%")
            return True
        else:
            print("❌ apply_shimming_to_b0_field returned None")
            return False
            
    except Exception as e:
        print(f"❌ apply_shimming_to_b0_field test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases for shimming"""
    print("\n" + "="*60)
    print("TESTING SHIMMING EDGE CASES")
    print("="*60)
    
    test_cases = [
        {
            'name': 'Empty cardiac mask',
            'b0_field': np.random.uniform(-100, 100, (50, 50, 50)),
            'cardiac_mask': np.zeros((50, 50, 50), dtype=bool)
        },
        {
            'name': 'Very small cardiac mask',
            'b0_field': np.random.uniform(-100, 100, (50, 50, 50)),
            'cardiac_mask': None  # Will create small mask
        },
        {
            'name': 'Uniform B0 field',
            'b0_field': np.ones((50, 50, 50)) * 42.0,
            'cardiac_mask': None  # Will create normal mask
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nEdge case {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            b0_field = test_case['b0_field']
            cardiac_mask = test_case['cardiac_mask']
            
            # Create cardiac mask if None
            if cardiac_mask is None:
                if test_case['name'] == 'Very small cardiac mask':
                    cardiac_mask = np.zeros_like(b0_field, dtype=bool)
                    cardiac_mask[20:25, 20:25, 20:25] = True  # Very small region
                else:
                    # Normal cardiac mask
                    center = np.array(b0_field.shape) // 2
                    x, y, z = np.ogrid[:b0_field.shape[0], :b0_field.shape[1], :b0_field.shape[2]]
                    distance = np.sqrt((x - center[0])**2 + (y - center[1])**2 + (z - center[2])**2)
                    cardiac_mask = distance < 15
            
            print(f"   B0 field range: [{np.min(b0_field):.1f}, {np.max(b0_field):.1f}]")
            print(f"   Cardiac mask voxels: {np.sum(cardiac_mask)}")
            
            if np.sum(cardiac_mask) == 0:
                print("   ⚠️ Empty cardiac mask - shimming should handle gracefully")
            
            # Test shimming
            shimmed_b0_map = apply_shimming_to_b0_field(b0_field, cardiac_mask)
            
            if shimmed_b0_map is not None:
                print("   ✅ Edge case handled successfully")
            else:
                print("   ❌ Edge case failed")
                
        except Exception as e:
            print(f"   ❌ Edge case failed: {str(e)}")

if __name__ == "__main__":
    print("Shimming Logging Test Suite")
    print("=" * 60)
    
    # Run tests
    success1 = test_shimming_optimization()
    success2 = test_apply_shimming_function()
    test_edge_cases()  # Edge cases don't return success/failure
    
    if success1 and success2:
        print("\n🎉 All shimming tests completed successfully!")
        print("The shimming functionality with detailed logging is working correctly.")
    else:
        print("\n❌ Some shimming tests failed. Check the logs for details.")
        sys.exit(1)
