XCAT Pipeline Complete Log - 2025-07-15 16:59:21
================================================================================
This log contains ALL output including print statements and logger messages
================================================================================

✅ Module imports successful

================================================================================
STARTING TIME FRAME 1
================================================================================

============================================================
PROCESSING TIME FRAME 1
============================================================
Input file: /home/<USER>/XCAT_Project/output/female_151/cine/female_151_cine.samp_act_1.vti
Output folder: /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01
✅ Input file found: /home/<USER>/XCAT_Project/output/female_151/cine/female_151_cine.samp_act_1.vti
Loading and preprocessing: /home/<USER>/XCAT_Project/output/female_151/cine/female_151_cine.samp_act_1.vti
Loaded VTI file: /home/<USER>/XCAT_Project/output/female_151/cine/female_151_cine.samp_act_1.vti
Image dimensions: (950, 500, 500)
Model info:
Origin: (0.0, 0.0, 0.0)
Dimensions: [500, 500, 950]
Spacing: [0.002, 0.002, 0.002]
Successfully loaded labels from file
Labels shape: (500, 500, 950)
Unique labels: [ 0.  1.  2.  3.  4.  5.  6.  7.  8.  9. 10. 11. 12. 13. 14. 15. 16. 17.
 18. 19. 20. 21. 22. 23. 24. 25. 26. 27. 28. 29. 30. 31. 32. 33. 34. 35.
 36. 37. 38. 40. 41. 42. 43. 44. 50. 51. 52. 53. 54. 57. 58. 59. 60. 61.
 62. 63. 64. 65. 66. 67. 69. 70.]
Applying label cleaning to correct wrong LV/RV wall labels...
Cleaning RV wall labels...
Found 8 RV wall components
Reassigned 130851 RV wall voxels to body tissue (label 9)
Wall voxels: 14979
Blood voxels: 13277
Found 34 slices containing cardiac structures (PRELIMINARY - uncleaned labels)
Preliminary z-range: 556 to 589 (34 slices)
Extended z-range for processing: 476 to 669 (194 slices)
Added 160 slices for safety margin
⚠️  REAL cardiac slice detection will happen AFTER label cleaning
Processing z-range: 476 to 669 (194 slices)

🔄 Starting slice processing...
🚀 Starting parallel processing of 194 slices using 8 processes

📋 Checking existing files...
   Completed: 134 slices
   To process: 60 slices

📋 Testing single slice to verify environment...
Processing slice 476...
Skipping warpImages3D for slice 476 - missing necessary cardiac structures
/home/<USER>/.conda/envs/xcat/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/home/<USER>/.conda/envs/xcat/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
Slice 476 tissue properties:
  PD range: 35.23-95.35
  T1 range: 41.74-1195.08
  T2 range: 5.00-130.18
  T2* range: 1.00-39.00
✅ Successfully processed slice 476
✅ Test successful! Starting parallel processing...
[Parallel(n_jobs=8)]: Using backend LokyBackend with 8 concurrent workers.
[Parallel(n_jobs=8)]: Done   2 tasks      | elapsed:    9.5s
[Parallel(n_jobs=8)]: Done   9 tasks      | elapsed:    9.9s
/home/<USER>/.local/lib/python3.8/site-packages/joblib/externals/loky/process_executor.py:752: UserWarning: A worker stopped while some jobs were given to the executor. This can be caused by a too short worker timeout or by a memory leak.
  warnings.warn(
[Parallel(n_jobs=8)]: Done  16 tasks      | elapsed:   15.0s
[Parallel(n_jobs=8)]: Done  25 tasks      | elapsed:   24.2s
[Parallel(n_jobs=8)]: Done  34 tasks      | elapsed:   29.5s
[Parallel(n_jobs=8)]: Done  45 tasks      | elapsed:   34.4s
[Parallel(n_jobs=8)]: Done  52 out of  60 | elapsed:   39.0s remaining:    6.0s
[Parallel(n_jobs=8)]: Done  60 out of  60 | elapsed:   43.7s finished

🎉 === Processing Complete ===
📊 Statistics:
   Total time: 0.8 minutes
   Total slices: 194
   Already completed: 134 (skipped)
   Newly processed: 60
   Newly successful: 60/60
   Total successful: 194/194
   Total success rate: 100.0%
   Average speed: 0.8 seconds/slice
✅ Slice processing completed: 194/194 successful

🔄 Starting binary conversion...

🔄 Starting binary conversion...
📋 Checking existing binary conversions...
   Already converted: 134 slices
   To convert: 60 slices
Converting slice 476 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_476 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_476
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 476 converted successfully
Converting slice 477 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_477 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_477
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 477 converted successfully
Converting slice 478 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_478 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_478
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 478 converted successfully
Converting slice 479 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_479 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_479
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 479 converted successfully
Converting slice 480 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_480 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_480
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 480 converted successfully
Converting slice 481 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_481 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_481
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 481 converted successfully
Converting slice 482 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_482 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_482
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 482 converted successfully
Converting slice 483 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_483 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_483
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 483 converted successfully
Converting slice 484 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_484 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_484
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 484 converted successfully
Converting slice 485 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_485 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_485
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 485 converted successfully
Converting slice 486 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_486 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_486
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 486 converted successfully
Converting slice 487 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_487 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_487
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 487 converted successfully
Converting slice 488 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_488 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_488
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 488 converted successfully
Converting slice 489 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_489 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_489
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 489 converted successfully
Converting slice 490 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_490 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_490
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 490 converted successfully
Converting slice 491 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_491 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_491
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 491 converted successfully
Converting slice 492 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_492 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_492
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 492 converted successfully
Converting slice 493 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_493 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_493
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 493 converted successfully
Converting slice 494 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_494 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_494
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 494 converted successfully
Converting slice 495 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_495 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_495
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 495 converted successfully
Converting slice 496 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_496 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_496
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 496 converted successfully
Converting slice 497 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_497 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_497
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 497 converted successfully
Converting slice 498 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_498 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_498
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 498 converted successfully
Converting slice 499 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_499 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_499
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 499 converted successfully
Converting slice 500 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_500 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_500
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 500 converted successfully
Converting slice 501 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_501 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_501
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 501 converted successfully
Converting slice 502 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_502 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_502
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 502 converted successfully
Converting slice 503 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_503 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_503
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 503 converted successfully
Converting slice 504 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_504 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_504
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 504 converted successfully
Converting slice 505 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_505 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_505
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 505 converted successfully
Converting slice 640 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_640 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_640
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 640 converted successfully
Converting slice 641 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_641 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_641
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 641 converted successfully
Converting slice 642 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_642 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_642
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 642 converted successfully
Converting slice 643 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_643 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_643
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 643 converted successfully
Converting slice 644 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_644 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_644
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 644 converted successfully
Converting slice 645 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_645 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_645
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 645 converted successfully
Converting slice 646 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_646 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_646
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 646 converted successfully
Converting slice 647 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_647 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_647
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 647 converted successfully
Converting slice 648 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_648 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_648
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 648 converted successfully
Converting slice 649 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_649 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_649
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 649 converted successfully
Converting slice 650 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_650 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_650
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 650 converted successfully
Converting slice 651 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_651 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_651
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 651 converted successfully
Converting slice 652 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_652 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_652
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 652 converted successfully
Converting slice 653 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_653 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_653
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 653 converted successfully
Converting slice 654 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_654 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_654
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 654 converted successfully
Converting slice 655 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_655 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_655
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 655 converted successfully
Converting slice 656 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_656 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_656
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 656 converted successfully
Converting slice 657 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_657 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_657
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 657 converted successfully
Converting slice 658 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_658 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_658
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 658 converted successfully
Converting slice 659 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_659 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_659
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 659 converted successfully
Converting slice 660 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_660 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_660
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 660 converted successfully
Converting slice 661 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_661 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_661
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 661 converted successfully
Converting slice 662 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_662 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_662
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 662 converted successfully
Converting slice 663 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_663 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_663
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 663 converted successfully
Converting slice 664 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_664 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_664
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 664 converted successfully
Converting slice 665 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_665 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_665
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 665 converted successfully
Converting slice 666 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_666 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_666
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 666 converted successfully
Converting slice 667 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_667 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_667
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 667 converted successfully
Converting slice 668 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_668 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_668
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 668 converted successfully
Converting slice 669 to binary format...
Converting VTI files from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_669 to binary in /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/bin_output/slice_669
✓ Saved labels.bin ((500, 500, 1))
✓ Saved pd/value.bin ((500, 500, 1))
✓ Saved t1/value.bin ((500, 500, 1))
✓ Saved t2/value.bin ((500, 500, 1))
✓ Saved t2s/value.bin ((500, 500, 1))
✓ Slice 669 converted successfully

🎉 === Binary Conversion Complete ===
📊 Statistics:
   Conversion time: 0.1 minutes
   Total slices: 194
   Already converted: 134 (skipped)
   Newly converted: 60
   Successful: 60
   Failed: 0
   Total successful: 194/194
   Total success rate: 100.0%

🔍 Validating binary conversion results...
Validation results:
   Valid: 194/194
   Invalid: 0/194
   Success rate: 100.0%

🔄 Starting cardiac processing (runCropper=True)...
✅ Cardiac processing section reached
Step 1: Cleaning LV/RV labels (BEFORE finding cardiac slices)...
Loading processed parameter maps from VTI files...
Loading processed parameters from /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01
Reconstructing volume with dimensions: (500, 500, 194)
Successfully loaded parameters for 194 slices
Loaded parameters result: True
      Labels before cleaning: [ 0  1  3  4  5  6  7  8  9 10 13 14 15 16 17 18 20 21 22 23 24 25 26 27
 28 29 30 31 32 33 34 35 36 40 41 42 43 50 51 52 53 57 58 64 65 66]
Applying label cleaning to volume...
Cleaning RV wall labels...
Found 0 RV wall components
Reassigned 0 RV wall voxels to body tissue (label 9)
      Labels after cleaning: [ 0  1  3  4  5  6  7  8  9 10 13 14 15 16 17 18 20 21 22 23 24 25 26 27
 28 29 30 31 32 33 34 35 36 40 41 42 43 50 51 52 53 57 58 64 65 66]
      Cardiac label counts: {1: 14979, 3: 1310, 4: 1627, 5: 8385, 6: 4892, 7: 6538, 8: 8872, 50: 25748}
      Found 53 slices with cardiac structures
      Cardiac slice range: 78 to 130
      Total cardiac slices: 53
      Cardiac voxels per slice: 38 to 2046
      Created cardiac mask with 72351 voxels
Loading B0 map from: /home/<USER>/XCAT_Project/output/female_151/female151_field_Hz_20250715_115352.npy
Original B0 map shape: (500, 500, 950)
Using provided slice range: 476 to 669
T2* volume shape: (500, 500, 194)
This ensures B0 field matches T2* volume dimensions
Extracting B0 map slices 476 to 669
T2* volume shape: (500, 500, 194)
Extracted B0 map shape: (500, 500, 194)
Applying orientation adjustments to B0 map...
Processing entire T2* volume with 3D B0 effects...
Calculating 3D field gradients...
complex calculation to get the range of r2prime-static: 0.00-201.08
simple calculation to get the range of r2prime-static: 0.00-1340.07
Saving updated T2*+ slices to binary files...

Saving slices:   0%|                                                                                 | 0/53 [00:00<?, ?it/s]Saved T2*+ slice 78 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_078/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 79 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_079/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 80 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_080/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 81 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_081/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 82 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_082/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 83 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_083/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 84 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_084/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 85 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_085/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 86 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_086/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 87 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_087/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 88 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_088/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 89 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_089/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 90 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_090/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 91 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_091/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 92 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_092/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 93 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_093/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 94 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_094/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 95 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_095/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 96 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_096/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 97 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_097/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 98 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_098/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 99 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_099/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 100 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_100/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 101 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_101/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 102 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_102/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 103 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_103/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 104 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_104/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 105 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_105/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 106 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_106/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 107 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_107/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 108 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_108/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 109 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_109/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 110 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_110/Tissue_properties_initial_frame/T2s_plus.bin

Saving slices:  62%|████████████████████████████████████████████▏                          | 33/53 [00:00<00:00, 325.03it/s]Saved T2*+ slice 111 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_111/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 112 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_112/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 113 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_113/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 114 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_114/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 115 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_115/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 116 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_116/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 117 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_117/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 118 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_118/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 119 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_119/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 120 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_120/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 121 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_121/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 122 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_122/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 123 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_123/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 124 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_124/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 125 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_125/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 126 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_126/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 127 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_127/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 128 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_128/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 129 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_129/Tissue_properties_initial_frame/T2s_plus.bin
Saved T2*+ slice 130 to /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/slice_130/Tissue_properties_initial_frame/T2s_plus.bin

Saving slices: 100%|███████████████████████████████████████████████████████████████████████| 53/53 [00:00<00:00, 333.50it/s]
Labels volume shape: (500, 500, 194)
Size of cardiac_mask: (500, 500, 194)
LV wall voxels: 14979
LV blood voxels: 8385
Total cardiac voxels: 72351
Found LV center at [228 258 103]
Cropped Labels: (500, 500, 194) → (150, 150, 150)
Cropped PD: (500, 500, 194) → (150, 150, 150)
Cropped T1: (500, 500, 194) → (150, 150, 150)
Cropped T2: (500, 500, 194) → (150, 150, 150)
Cropped T2s: (500, 500, 194) → (150, 150, 150)
Cropped T2*: (500, 500, 194) → (150, 150, 150)
Cropped T2*+: (500, 500, 194) → (150, 150, 150)
      Loading B0 field from: /home/<USER>/XCAT_Project/output/female_151/female151_field_Hz_20250715_115352.npy
      Original B0 field shape: (500, 500, 950)
      Using processed z-range: 476 to 669
      This matches the processed volume dimensions
      Extracted B0 region: (500, 500, 194)
      Cardiac mask shape: (500, 500, 194)
      Applying shimming to B0 field...
      Using cardiac mask directly (shapes match)
Optimizing B0 shimming...
Number of voxels in cardiac mask: 72351

============================================================
B0 SHIMMING OPTIMIZATION RESULTS
============================================================
Cardiac region voxels: 72351
Original B0 field RMS: 96.51 Hz
Shimmed B0 field RMS: 60.43 Hz
RMS improvement: 37.4%
Original B0 std dev: 96.51 Hz
Shimmed B0 std dev: 60.43 Hz
Original B0 range: 1130.54 Hz
Shimmed B0 range: 1130.71 Hz

Optimization method: Least squares (trf)
Optimization success: True
Optimization cost: 132117886.438580
Optimization iterations: 0

----------------------------------------
OPTIMIZED SHIM COEFFICIENTS
----------------------------------------
Coil     Coefficient  Units   
----------------------------
Z0         178.8094    Hz
X         1499.9625    Hz
Y         -764.1929    Hz
Z         -678.6749    Hz
Z2       -1352.3909    Hz
ZX       -3345.1852    Hz
ZY        8404.8243    Hz
C2        2464.6681    Hz
S2       -1217.9742    Hz

----------------------------------------
REQUIRED CURRENTS FOR SHIMMING
----------------------------------------
Coil     Current      Sensitivity  Units   
--------------------------------------------
Z0           0.0295       6058.00    A
X         1499.9625          1.00    A
Y         -764.1929          1.00    A
Z         -678.6749          1.00    A
Z2        -194.8129          6.94    A
ZX        -138.5170         24.15    A
ZY         348.0259         24.15    A
C2         677.1066          3.64    A
S2        -334.6083          3.64    A

Estimated total power: 4043174.8425 W (assuming 1Ω resistance)
============================================================
Shimming optimization completed successfully
Original shimmed B0 map shape: (500, 500, 194)
Transposed shimmed B0 map shape: (500, 500, 194)
Extracting shimmed B0 map slices 0 to 193
Extracted shimmed B0 map shape: (500, 500, 194)
Applying orientation adjustments to shimmed B0 map...
B0 field cropped from (500, 500, 194) to (150, 150, 150)
B0 field cropped from (500, 500, 194) to (150, 150, 150)
      Cropped B0 field: (150, 150, 150)
      Cropped shimmed B0 field: (150, 150, 150)
Saved cardiac parameters to: /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/cardiac_region_new/cardiac_volume.npz
Saved metadata to: /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/cardiac_region_new/cardiac_metadata_frame_01.txt

Summary of saved cardiac parameter maps:
Parameter    Shape           Type         Range               
------------------------------------------------------------
PD           (150, 150, 150) float32      [35.12, 97.30]
T1           (150, 150, 150) float32      [20.25, 1399.32]
T2           (150, 150, 150) float32      [4.16, 276.13]
T2star       (150, 150, 150) float32      [1.00, 66.00]
T2star_plus  (150, 150, 150) float32      [1.00, 63.39]
Labels       (150, 150, 150) uint8        [0.00, 66.00]
B0           (150, 150, 150) float32      [-853.23, 544.58]
ShimmedB0    (150, 150, 150) float64      [-1854.51, 1427.39]

   Summary of cardiac parameter maps:
   Parameter    Shape           Type         Range               
   ------------------------------------------------------------
   Labels       (150, 150, 150) uint8        [0.00, 66.00]
   PD           (150, 150, 150) float32      [35.12, 97.30]
   T1           (150, 150, 150) float32      [20.25, 1399.32]
   T2           (150, 150, 150) float32      [4.16, 276.13]
   T2s          (150, 150, 150) float32      [1.00, 66.00]
   T2*          (150, 150, 150) float32      [1.00, 66.00]
   T2*+         (150, 150, 150) float32      [1.00, 63.39]
   B0           (150, 150, 150) float32      [-853.23, 544.58]
   ShimmedB0    (150, 150, 150) float64      [-1854.51, 1427.39]

✅ Time frame 1 processing complete!
✅ Time frame 1 completed successfully

================================================================================
MULTI-TIMEFRAME PROCESSING COMPLETE
================================================================================
Total time: 2.1 minutes
Successful frames: 1/1
Failed frames: 0/1
Success rate: 100.0%

Log files created:
  Main log: logs/xcat_pipeline_20250715_165921.log
  Error log: logs/xcat_errors_20250715_165921.log
  Complete log (ALL output): logs/xcat_complete_20250715_165921.log
  Log directory: logs
