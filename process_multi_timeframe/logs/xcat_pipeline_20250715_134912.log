2025-07-15 13:49:12,044 - XCAT_Pipeline - INFO - XCAT Pipeline Logger initialized
2025-07-15 13:49:12,044 - XCAT_Pipeline - INFO - Main log file: logs/xcat_pipeline_20250715_134912.log
2025-07-15 13:49:12,044 - XCAT_Pipeline - INFO - Error log file: logs/xcat_errors_20250715_134912.log
2025-07-15 13:49:12,044 - XCAT_Pipeline - INFO - All print statements will be captured in log files
2025-07-15 13:49:12,044 - XCAT_Pipeline - INFO - Starting multi-timeframe XCAT processing
2025-07-15 13:49:12,044 - XCAT_Pipeline - INFO - Time frames to process: [1]
2025-07-15 13:49:12,044 - XCAT_Pipeline - INFO - Processing flags: Warper=True, Texturizer=True, Converter=True, Cropper=True
2025-07-15 13:49:12,044 - XCAT_Pipeline - INFO - Parallel jobs: 8
2025-07-15 13:53:48,205 - XCAT_Pipeline - INFO - 🔄 Starting Cardiac Processing (label cleaning, cardiac slice detection, T2* update, cropping, NPZ saving)...
2025-07-15 13:53:48,206 - XCAT_Pipeline - INFO - Step 1: Cleaning LV/RV labels (BEFORE finding cardiac slices)...
2025-07-15 13:53:48,206 - XCAT_Pipeline - INFO - Loading processed parameter maps from VTI files...
2025-07-15 13:54:21,280 - XCAT_Pipeline - INFO - Parameter Summary:
2025-07-15 13:54:21,280 - XCAT_Pipeline - INFO - ----------------------------------------
2025-07-15 13:54:21,280 - XCAT_Pipeline - INFO -   Loaded parameters: ['Labels', 'PD', 'T1', 'T2', 'T2s', 'T2*']
2025-07-15 13:54:21,280 - XCAT_Pipeline - INFO -   Volume shape: (500, 500, 350)
2025-07-15 13:54:24,227 - XCAT_Pipeline - INFO - Label cleaning completed - now have correct cardiac labels
2025-07-15 13:54:24,227 - XCAT_Pipeline - INFO - Step 2: Finding slices with cardiac structures from CLEANED labels...
2025-07-15 13:54:30,309 - XCAT_Pipeline - INFO - Found cardiac structures in 135 slices
2025-07-15 13:54:30,309 - XCAT_Pipeline - INFO - Cardiac z-range from cleaned labels: 49 to 298
2025-07-15 13:54:30,309 - XCAT_Pipeline - INFO - Original z-range was: 506 to 855
2025-07-15 13:54:30,309 - XCAT_Pipeline - INFO - Using cardiac z-indices for all subsequent processing: 135 slices
2025-07-15 13:54:30,309 - XCAT_Pipeline - INFO - Step 3: Creating cardiac mask from cleaned labels...
2025-07-15 13:54:30,867 - XCAT_Pipeline - INFO - Created cardiac mask with 207494 voxels
2025-07-15 13:54:30,867 - XCAT_Pipeline - INFO - Step 4: Updating T2* with B0 field effects using cardiac slices...
2025-07-15 13:54:30,867 - XCAT_Pipeline - INFO - Found B0 map: /home/<USER>/XCAT_Project/output/female_151/female151_field_Hz_20250715_115352.npy
2025-07-15 13:54:30,868 - XCAT_Pipeline - INFO - Using cardiac slice range for B0 processing: (49, 298)
2025-07-15 13:54:36,565 - XCAT_Pipeline - INFO - T2* update completed successfully using cardiac slices
2025-07-15 13:54:36,566 - XCAT_Pipeline - INFO - Step 5: Cropping cardiac region using cleaned labels...
2025-07-15 13:54:38,166 - XCAT_Pipeline - INFO - Cardiac cropping completed: (150, 150, 150)
2025-07-15 13:54:38,166 - XCAT_Pipeline - INFO - Step 6: Processing B0 fields using processed z-range...
2025-07-15 13:54:38,166 - XCAT_Pipeline - INFO - Using processed z-range (506, 855) to match volume shape (500, 500, 350)
2025-07-15 13:54:38,553 - XCAT_Pipeline - INFO - Starting B0 shimming optimization...
2025-07-15 13:54:38,602 - XCAT_Pipeline - INFO - Created B0 cardiac mask: (500, 500, 350), 207494 voxels
2025-07-15 13:54:48,915 - XCAT_Pipeline - INFO - B0 shimming optimization completed
2025-07-15 13:54:48,920 - XCAT_Pipeline - INFO - Step 7: Saving cardiac NPZ file...
2025-07-15 13:54:52,592 - XCAT_Pipeline - INFO - ✅ NPZ file saved: /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/cardiac_region_new/cardiac_volume.npz
2025-07-15 13:54:52,592 - XCAT_Pipeline - INFO - ✅ Metadata saved: /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/cardiac_region_new/cardiac_metadata_frame_01.txt
2025-07-15 13:54:52,631 - XCAT_Pipeline - INFO - ✅ Cardiac Processing completed
