2025-07-15 16:29:44,579 - XCAT_Pipeline - INFO - XCAT Pipeline Logger initialized
2025-07-15 16:29:44,579 - XCAT_Pipeline - INFO - Main log file: logs/xcat_pipeline_20250715_162944.log
2025-07-15 16:29:44,579 - XCAT_Pipeline - INFO - Error log file: logs/xcat_errors_20250715_162944.log
2025-07-15 16:29:44,579 - XCAT_Pipeline - INFO - All print statements will be captured in log files
2025-07-15 16:29:44,579 - XCAT_Pipeline - INFO - 🔄 Starting Cardiac Processing (label cleaning, cardiac slice detection, T2* update, cropping, NPZ saving)...
2025-07-15 16:29:44,579 - XCAT_Pipeline - INFO - Step 1: Cleaning LV/RV labels (BEFORE finding cardiac slices)...
2025-07-15 16:29:44,579 - XCAT_Pipeline - INFO - Loading processed parameter maps from VTI files...
2025-07-15 16:29:47,884 - XCAT_Pipeline - INFO - Parameter Summary:
2025-07-15 16:29:47,884 - XCAT_Pipeline - INFO - ----------------------------------------
2025-07-15 16:29:47,884 - XCAT_Pipeline - INFO -   Loaded parameters: ['Labels', 'PD', 'T1', 'T2', 'T2s', 'T2*']
2025-07-15 16:29:47,884 - XCAT_Pipeline - INFO -   Volume shape: (500, 500, 350)
2025-07-15 16:29:50,887 - XCAT_Pipeline - INFO - Label cleaning completed - now have correct cardiac labels
2025-07-15 16:29:50,887 - XCAT_Pipeline - INFO - Step 2: REAL cardiac slice detection using CLEANED labels...
2025-07-15 16:29:50,887 - XCAT_Pipeline - INFO - (Previous detection in data loading used uncleaned labels - this is the correct one)
2025-07-15 16:29:56,976 - XCAT_Pipeline - ERROR - No cardiac slices found in cleaned labels - using original z_indices as fallback
2025-07-15 16:29:56,976 - XCAT_Pipeline - INFO - Using CLEANED cardiac z-indices for all subsequent processing: 350 slices
2025-07-15 16:29:56,976 - XCAT_Pipeline - INFO - Step 3: Creating cardiac mask from cleaned labels...
2025-07-15 16:29:57,549 - XCAT_Pipeline - INFO - Created cardiac mask with 0 voxels
2025-07-15 16:29:57,549 - XCAT_Pipeline - INFO - Step 4: Updating T2* with B0 field effects using cardiac slices...
2025-07-15 16:29:57,549 - XCAT_Pipeline - INFO - Found B0 map: /home/<USER>/XCAT_Project/output/female_151/female151_field_Hz_20250715_115352.npy
2025-07-15 16:29:57,549 - XCAT_Pipeline - INFO - Using cardiac slice range for B0 processing: (506, 855)
2025-07-15 16:30:04,135 - XCAT_Pipeline - INFO - T2* update completed successfully using cardiac slices
2025-07-15 16:30:04,135 - XCAT_Pipeline - INFO - Step 5: Cropping cardiac region using cleaned labels...
2025-07-15 16:30:04,525 - XCAT_Pipeline - INFO - Cardiac cropping completed: (150, 150, 150)
2025-07-15 16:30:04,525 - XCAT_Pipeline - INFO - Step 6: Processing B0 fields using processed z-range...
2025-07-15 16:30:04,525 - XCAT_Pipeline - INFO - Using processed z-range (506, 855) to match volume shape (500, 500, 350)
2025-07-15 16:30:04,911 - XCAT_Pipeline - INFO - Starting B0 shimming optimization...
2025-07-15 16:30:04,958 - XCAT_Pipeline - INFO - Created B0 cardiac mask: (500, 500, 350), 0 voxels
2025-07-15 16:30:14,805 - XCAT_Pipeline - INFO - B0 shimming optimization completed
2025-07-15 16:30:14,810 - XCAT_Pipeline - INFO - Step 7: Saving cardiac NPZ file...
2025-07-15 16:30:15,430 - XCAT_Pipeline - INFO - ✅ NPZ file saved: /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/cardiac_region_new/cardiac_volume.npz
2025-07-15 16:30:15,430 - XCAT_Pipeline - INFO - ✅ Metadata saved: /home/<USER>/XCAT_Project/outputData/female_151/cine/frame_01/cardiac_region_new/cardiac_metadata_frame_01.txt
2025-07-15 16:30:15,489 - XCAT_Pipeline - INFO - ✅ Cardiac Processing completed
