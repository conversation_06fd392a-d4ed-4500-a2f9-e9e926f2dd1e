#!/usr/bin/env python3
"""
Comprehensive test for the complete XCAT processing pipeline
Tests T2* updates, label cleaning, cardiac cropping, and NPZ saving
"""

import os
import sys
import numpy as np

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from t2star_updater import update_t2star_with_b0_3d, process_t2star_volume_3d
from label_cleaner import clean_rv_wall_mask, apply_label_cleaning_to_volume
from cardiac_cropper import crop_cardiac_region, save_cardiac_npz

def create_comprehensive_test_data():
    """Create comprehensive synthetic test data"""
    print("Creating comprehensive synthetic test data...")
    
    # Create a 400x400x400 volume
    volume_shape = (400, 400, 400)
    
    # Create synthetic parameter maps
    params = {
        'Labels': np.zeros(volume_shape, dtype=np.uint8),
        'PD': np.random.uniform(50, 100, volume_shape).astype(np.float32),
        'T1': np.random.uniform(500, 1500, volume_shape).astype(np.float32),
        'T2': np.random.uniform(20, 200, volume_shape).astype(np.float32),
        'T2s': np.random.uniform(10, 50, volume_shape).astype(np.float32),
        'T2*': np.random.uniform(10, 50, volume_shape).astype(np.float32)
    }
    
    # Create a synthetic cardiac region in the center
    center = np.array(volume_shape) // 2
    cardiac_radius = 80
    
    # Create spherical cardiac region
    x, y, z = np.ogrid[:volume_shape[0], :volume_shape[1], :volume_shape[2]]
    distance = np.sqrt((x - center[0])**2 + (y - center[1])**2 + (z - center[2])**2)
    
    # Create cardiac mask
    cardiac_mask = distance < cardiac_radius
    
    # Assign cardiac labels with some wrong RV wall components
    params['Labels'][cardiac_mask] = 1  # LV wall
    
    # Create LV blood region
    inner_mask = distance < cardiac_radius * 0.6
    params['Labels'][inner_mask] = 5  # LV blood
    
    # Create RV blood region (offset)
    rv_center = center + np.array([30, 0, 0])
    rv_distance = np.sqrt((x - rv_center[0])**2 + (y - rv_center[1])**2 + (z - rv_center[2])**2)
    rv_blood_mask = rv_distance < 25
    params['Labels'][rv_blood_mask] = 6  # RV blood
    
    # Create RV wall (some connected, some not - to test cleaning)
    rv_wall_mask = (rv_distance < 35) & (rv_distance >= 25)
    params['Labels'][rv_wall_mask] = 2  # RV wall
    
    # Add some disconnected RV wall components (should be removed)
    disconnected_rv = (distance > cardiac_radius + 20) & (distance < cardiac_radius + 30)
    params['Labels'][disconnected_rv] = 2  # Wrong RV wall
    
    # Create synthetic B0 field
    b0_field = np.random.uniform(-200, 200, volume_shape).astype(np.float32)
    
    # Add some spatial variation to B0 field
    b0_field += 50 * np.sin(x / 50) * np.cos(y / 50) * np.sin(z / 50)
    
    print(f"Created test data with shape: {volume_shape}")
    print(f"Cardiac voxels: {np.sum(cardiac_mask)}")
    print(f"RV wall voxels (before cleaning): {np.sum(params['Labels'] == 2)}")
    print(f"RV blood voxels: {np.sum(params['Labels'] == 6)}")
    
    return params, cardiac_mask, b0_field

def test_t2star_updates():
    """Test T2* update functionality"""
    print("\n" + "=" * 60)
    print("Testing T2* Update Functionality")
    print("=" * 60)
    
    params, cardiac_mask, b0_field = create_comprehensive_test_data()
    
    try:
        # Test T2* update with B0 field
        print("1. Testing T2* update with B0 field...")
        t2star_updated = update_t2star_with_b0_3d(
            params['T2s'], params['T2'], b0_field, voxel_size=2.0
        )
        
        print("✅ T2* update successful!")
        print(f"   Original T2* range: [{np.min(params['T2s']):.2f}, {np.max(params['T2s']):.2f}]")
        print(f"   Updated T2* range: [{np.min(t2star_updated):.2f}, {np.max(t2star_updated):.2f}]")
        
        # Verify the update made changes
        if not np.array_equal(params['T2s'], t2star_updated):
            print("   T2* values were successfully modified by B0 field")
        else:
            print("   Warning: T2* values unchanged")
        
        return True
        
    except Exception as e:
        print(f"❌ T2* update failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_label_cleaning():
    """Test label cleaning functionality"""
    print("\n" + "=" * 60)
    print("Testing Label Cleaning Functionality")
    print("=" * 60)
    
    params, cardiac_mask, b0_field = create_comprehensive_test_data()
    
    try:
        # Test RV wall cleaning
        print("1. Testing RV wall cleaning...")
        original_rv_count = np.sum(params['Labels'] == 2)
        cleaned_rv_mask = clean_rv_wall_mask(params['Labels'])
        cleaned_rv_count = np.sum(cleaned_rv_mask)
        
        print(f"   Original RV wall voxels: {original_rv_count}")
        print(f"   Cleaned RV wall voxels: {cleaned_rv_count}")
        print(f"   Removed RV wall voxels: {original_rv_count - cleaned_rv_count}")
        
        # Test full label cleaning
        print("2. Testing complete label cleaning...")
        cleaned_params = apply_label_cleaning_to_volume(params, body_label=9, debug=False)
        
        final_rv_count = np.sum(cleaned_params['Labels'] == 2)
        body_increase = np.sum(cleaned_params['Labels'] == 9) - np.sum(params['Labels'] == 9)
        
        print(f"   Final RV wall voxels: {final_rv_count}")
        print(f"   Body tissue increase: {body_increase}")
        
        print("✅ Label cleaning successful!")
        return True
        
    except Exception as e:
        print(f"❌ Label cleaning failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_pipeline():
    """Test the complete pipeline with all components"""
    print("\n" + "=" * 60)
    print("Testing Complete Pipeline")
    print("=" * 60)
    
    params, cardiac_mask, b0_field = create_comprehensive_test_data()
    
    try:
        # Step 1: Label cleaning
        print("1. Applying label cleaning...")
        params = apply_label_cleaning_to_volume(params, body_label=9, debug=False)
        
        # Step 2: T2* update
        print("2. Updating T2* with B0 field...")
        t2star_plus = update_t2star_with_b0_3d(
            params['T2s'], params['T2'], b0_field, voxel_size=2.0
        )
        params['T2*+'] = t2star_plus
        
        # Step 3: Cardiac cropping
        print("3. Cropping cardiac region...")
        cardiac_params = crop_cardiac_region(
            params, cardiac_mask, 
            crop_size=(150, 150, 150), 
            debug=False
        )
        
        # Step 4: Create synthetic B0 fields for NPZ saving
        print("4. Creating synthetic B0 fields...")
        b0_cardiac = np.random.uniform(-423, 293, (150, 150, 150)).astype(np.float32)
        shimmed_b0_cardiac = np.random.uniform(-274, 1223, (150, 150, 150)).astype(np.float64)
        
        # Step 5: Save NPZ file
        print("5. Saving cardiac NPZ file...")
        output_dir = "/tmp/test_complete_pipeline"
        os.makedirs(output_dir, exist_ok=True)
        
        npz_path, metadata_path = save_cardiac_npz(
            cardiac_params, 
            b0_cardiac=b0_cardiac, 
            shimmed_b0_cardiac=shimmed_b0_cardiac,
            output_folder_bin=output_dir, 
            time_frame=1, 
            subject_id="test_complete"
        )
        
        # Step 6: Verify saved file
        print("6. Verifying saved NPZ file...")
        if os.path.exists(npz_path):
            loaded_data = np.load(npz_path)
            expected_params = ['PD', 'T1', 'T2', 'T2*', 'T2*+', 'Labels', 'B0', 'ShimmedB0']
            
            print(f"   NPZ file saved: {npz_path}")
            print(f"   Saved parameters: {list(loaded_data.keys())}")
            
            # Check if we have all expected parameters
            missing_params = [p for p in expected_params if p not in loaded_data.keys()]
            if missing_params:
                print(f"   Missing parameters: {missing_params}")
            else:
                print("   ✅ All expected parameters present")
            
            # Verify shapes
            for param_name in loaded_data.keys():
                if param_name in ['B0', 'ShimmedB0']:
                    expected_shape = (150, 150, 150)
                else:
                    expected_shape = cardiac_params[param_name].shape
                
                actual_shape = loaded_data[param_name].shape
                if actual_shape == expected_shape:
                    print(f"   ✅ {param_name}: {actual_shape}")
                else:
                    print(f"   ❌ {param_name}: {actual_shape} (expected {expected_shape})")
            
            print("✅ Complete pipeline test successful!")
            return True
        else:
            print("❌ NPZ file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Complete pipeline test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Complete XCAT Pipeline Test Suite")
    print("=" * 60)
    
    # Run individual tests
    success1 = test_t2star_updates()
    success2 = test_label_cleaning()
    success3 = test_complete_pipeline()
    
    if success1 and success2 and success3:
        print("\n🎉 All tests completed successfully!")
        print("The complete XCAT processing pipeline is working correctly.")
        print("\nThe pipeline now includes:")
        print("✅ T2* updates with B0 field effects")
        print("✅ LV/RV label cleaning")
        print("✅ Cardiac region cropping (150×150×150)")
        print("✅ NPZ export with all parameters including B0 fields")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        sys.exit(1)
