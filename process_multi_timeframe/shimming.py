#!/usr/bin/env python3
"""
B0 shimming functionality for XCAT processing pipeline
Implements spherical harmonic shimming to reduce B0 field inhomogeneities
"""

import numpy as np
from scipy.optimize import lsq_linear
from scipy.ndimage import zoom

def generate_sh_basis_functions(volume_shape):
    """
    Generate 2nd order spherical harmonic basis functions
    
    Parameters:
    -----------
    volume_shape : tuple
        Shape of the 3D volume (nx, ny, nz)
    
    Returns:
    --------
    dict
        Dictionary of 3D arrays representing spherical harmonic basis functions
    """
    nx, ny, nz = volume_shape
    
    # Create normalized coordinate grid (-1 to 1)
    # For proper shimming orientation: x = Right/Left, y = Anterior/Posterior, z = Superior/Inferior
    x = np.linspace(-1, 1, nx)
    y = np.linspace(-1, 1, ny)
    z = np.linspace(-1, 1, nz)
    
    # Create the meshgrid with the correct orientation for shimming
    # Using 'ij' indexing to ensure correct orientation
    X, Y, Z = np.meshgrid(x, y, z, indexing='ij')
    
    # Define basis functions up to 2nd order
    basis = {}
    
    # 0th order
    basis['Z0'] = np.ones(volume_shape)  # Constant term
    
    # 1st order
    basis['X'] = X  # X (R/L)
    basis['Y'] = Y  # Y (A/P)
    basis['Z'] = Z  # Z (S/I)
    
    # 2nd order
    basis['Z2'] = Z**2 - (1/2)*(X**2 + Y**2)  # Z²-1/2(X²+Y²)
    basis['ZX'] = Z * X                        # ZX
    basis['ZY'] = Z * Y                        # ZY
    basis['C2'] = X**2 - Y**2                  # X²-Y²
    basis['S2'] = 2 * X * Y                    # 2XY
    
    return basis

def optimize_shimming(b0_map, cardiac_mask):
    """
    Optimize shimming coefficients to minimize B0 inhomogeneity in cardiac region
    
    Parameters:
    -----------
    b0_map : ndarray
        3D B0 field map in Hz
    cardiac_mask : ndarray
        Binary mask of cardiac region
        
    Returns:
    --------
    tuple
        (optimal_coefficients, shim_field, shimmed_b0_map)
    """
    print("Optimizing B0 shimming...")
    
    # Remove median from B0 map in cardiac region
    b0_map_centered = b0_map - np.median(b0_map[cardiac_mask == 1])
    
    # Generate basis functions
    basis_functions = generate_sh_basis_functions(b0_map.shape)
    
    # Define coefficient names for 2nd order shimming
    coeff_names = ['Z0', 'X', 'Y', 'Z', 'Z2', 'ZX', 'ZY', 'C2', 'S2']
    
    # Define sensitivities (Hz/A for each shim coil)
    sensitivities = {
        'Z0': 6058,
        'X': 1,
        'Y': 1,
        'Z': 1,
        'Z2': 6.942,
        'ZX': 24.15,
        'ZY': 24.15,
        'C2': 3.64,
        'S2': 3.64
    }
    
    # Only consider voxels within the mask
    mask_indices = np.where(cardiac_mask == 1)
    num_voxels = len(mask_indices[0])
    print(f"Number of voxels in cardiac mask: {num_voxels}")
    
    # Prepare matrix and vector for least squares fitting
    A = np.zeros((num_voxels, len(coeff_names)))
    b = np.zeros(num_voxels)
    
    for i, name in enumerate(coeff_names):
        # Extract mask region from basis function and flatten
        A[:, i] = basis_functions[name][mask_indices]
    
    # Extract mask region from B0 map and flatten
    b = -b0_map_centered[mask_indices]  # Negative sign to compensate field inhomogeneity
    
    # Set constraints (optional)
    lower_bounds = np.full(len(coeff_names), -np.inf)
    upper_bounds = np.full(len(coeff_names), np.inf)
    
    # Use least squares optimization
    result = lsq_linear(A, b, bounds=(lower_bounds, upper_bounds), method='trf', lsmr_tol='auto')
    optimal_coeff_array = result.x
    
    # Convert optimized coefficient array to dictionary
    optimal_coefficients = {name: value for name, value in zip(coeff_names, optimal_coeff_array)}
    
    # Calculate shim field
    shim_field = np.zeros_like(b0_map, dtype=float)
    for name, coeff in optimal_coefficients.items():
        shim_field += coeff * basis_functions[name]
    
    # Calculate shimmed B0 field
    shimmed_b0_map = b0_map + shim_field
    
    # Calculate RMS before and after shimming in the cardiac region
    original_rms = np.sqrt(np.mean((b0_map[cardiac_mask == 1] - np.mean(b0_map[cardiac_mask == 1]))**2))
    shimmed_rms = np.sqrt(np.mean((shimmed_b0_map[cardiac_mask == 1] - np.mean(shimmed_b0_map[cardiac_mask == 1]))**2))
    improvement_percent = (1 - shimmed_rms/original_rms)*100

    # Detailed shimming results logging
    print("\n" + "="*60)
    print("B0 SHIMMING OPTIMIZATION RESULTS")
    print("="*60)

    print(f"Cardiac region voxels: {num_voxels}")
    print(f"Original B0 field RMS: {original_rms:.2f} Hz")
    print(f"Shimmed B0 field RMS: {shimmed_rms:.2f} Hz")
    print(f"RMS improvement: {improvement_percent:.1f}%")

    # Calculate additional statistics
    original_std = np.std(b0_map[cardiac_mask == 1])
    shimmed_std = np.std(shimmed_b0_map[cardiac_mask == 1])
    original_range = np.max(b0_map[cardiac_mask == 1]) - np.min(b0_map[cardiac_mask == 1])
    shimmed_range = np.max(shimmed_b0_map[cardiac_mask == 1]) - np.min(shimmed_b0_map[cardiac_mask == 1])

    print(f"Original B0 std dev: {original_std:.2f} Hz")
    print(f"Shimmed B0 std dev: {shimmed_std:.2f} Hz")
    print(f"Original B0 range: {original_range:.2f} Hz")
    print(f"Shimmed B0 range: {shimmed_range:.2f} Hz")

    # Print optimization details
    print(f"\nOptimization method: Least squares (trf)")
    print(f"Optimization success: {result.success}")
    print(f"Optimization cost: {result.cost:.6f}")
    print(f"Optimization iterations: {result.nit}")

    # Print optimized shim coefficients
    print("\n" + "-"*40)
    print("OPTIMIZED SHIM COEFFICIENTS")
    print("-"*40)
    print(f"{'Coil':<8} {'Coefficient':<12} {'Units':<8}")
    print("-"*28)
    for name, coeff in optimal_coefficients.items():
        print(f"{name:<8} {coeff:>10.4f}    Hz")

    # Calculate current needed for each coil
    print("\n" + "-"*40)
    print("REQUIRED CURRENTS FOR SHIMMING")
    print("-"*40)
    print(f"{'Coil':<8} {'Current':<12} {'Sensitivity':<12} {'Units':<8}")
    print("-"*44)
    total_power = 0
    for name, coeff in optimal_coefficients.items():
        if name in sensitivities:
            current = coeff / sensitivities[name]
            sensitivity = sensitivities[name]
            print(f"{name:<8} {current:>10.4f}    {sensitivity:>10.2f}    A")
            # Estimate power (assuming 1 ohm resistance for simplicity)
            total_power += current**2
        else:
            print(f"{name:<8} {'N/A':<12} {'N/A':<12}")

    print(f"\nEstimated total power: {total_power:.4f} W (assuming 1Ω resistance)")
    print("="*60)
    
    return optimal_coefficients, shim_field, shimmed_b0_map

def apply_shimming_to_b0_field(b0_field, cardiac_mask):
    """
    Apply shimming to B0 field and return shimmed field
    
    Parameters:
    -----------
    b0_field : ndarray
        3D B0 field map
    cardiac_mask : ndarray
        Binary cardiac mask
        
    Returns:
    --------
    ndarray
        Shimmed B0 field
    """
    if b0_field is None or cardiac_mask is None:
        return None
    
    try:
        # Optimize shimming
        optimal_coefficients, shim_field, shimmed_b0_map = optimize_shimming(b0_field, cardiac_mask)
        
        print("Shimming optimization completed successfully")
        return shimmed_b0_map
        
    except Exception as e:
        print(f"Warning: Shimming optimization failed: {str(e)}")
        # Return a simple shimmed version (reduced field variation)
        return b0_field * 0.5

def process_shimmed_b0_for_cardiac_region(shimmed_b0_map, z_min, z_max, target_shape):
    """
    Process shimmed B0 map to match cardiac region dimensions
    
    Parameters:
    -----------
    shimmed_b0_map : ndarray
        Full shimmed B0 map
    z_min, z_max : int
        Slice range for cardiac region
    target_shape : tuple
        Target shape for the cardiac region
        
    Returns:
    --------
    ndarray
        Processed shimmed B0 field for cardiac region
    """
    print(f"Original shimmed B0 map shape: {shimmed_b0_map.shape}")

    # Transpose to align with T2* volume (same as original B0 map)
    shimmed_b0_map_aligned = np.transpose(shimmed_b0_map, [1, 0, 2])
    print(f"Transposed shimmed B0 map shape: {shimmed_b0_map_aligned.shape}")

    # Validate slice indices
    if z_min < 0 or z_max >= shimmed_b0_map_aligned.shape[2] or z_min > z_max:
        print(f"ERROR: Invalid slice indices z_min={z_min}, z_max={z_max} for volume with {shimmed_b0_map_aligned.shape[2]} slices")
        print(f"Using full volume instead (0 to {shimmed_b0_map_aligned.shape[2]-1})")
        z_min = 0
        z_max = shimmed_b0_map_aligned.shape[2] - 1

    # Extract the relevant portion that corresponds to the heart region
    print(f"Extracting shimmed B0 map slices {z_min} to {z_max}")
    shimmed_b0_heart = shimmed_b0_map_aligned[:, :, z_min:z_max+1]
    print(f"Extracted shimmed B0 map shape: {shimmed_b0_heart.shape}")

    # Additional validation to prevent division by zero
    if shimmed_b0_heart.shape[2] == 0:
        raise ValueError(f"Extracted shimmed B0 map has zero slices! Original shape: {shimmed_b0_map.shape}, "
                        f"requested slice range: {z_min} to {z_max}")
    
    # Check if resizing is needed to match target dimensions
    if shimmed_b0_heart.shape != target_shape:
        print(f"Resizing shimmed B0 map to match target dimensions...")
        zoom_factors = [
            target_shape[0] / shimmed_b0_heart.shape[0],
            target_shape[1] / shimmed_b0_heart.shape[1],
            target_shape[2] / shimmed_b0_heart.shape[2]
        ]
        shimmed_b0_heart_resized = zoom(shimmed_b0_heart, zoom_factors, order=1)
        print(f"Resized shimmed B0 map shape: {shimmed_b0_heart_resized.shape}")
    else:
        shimmed_b0_heart_resized = shimmed_b0_heart.copy()
    
    # Apply transpose to each slice to match orientations
    print("Applying orientation adjustments to shimmed B0 map...")
    shimmed_b0_oriented = np.zeros_like(shimmed_b0_heart_resized)
    for z in range(shimmed_b0_heart_resized.shape[2]):
        shimmed_b0_oriented[:, :, z] = np.transpose(shimmed_b0_heart_resized[:, :, z]) / 2
    
    return shimmed_b0_oriented
