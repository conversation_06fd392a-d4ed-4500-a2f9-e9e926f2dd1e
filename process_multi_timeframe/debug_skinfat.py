#!/usr/bin/env python3
"""
Debug script to test the addSkinFat function and understand why it's not working
"""

import os
import sys
import numpy as np
import vtk
from vtk.util.numpy_support import vtk_to_numpy
from scipy.ndimage import distance_transform_edt as dtf

# Add paths for imports
sys.path.append('/home/<USER>/XCAT_Project/myTexturizer')

def load_vti_labels(vti_path):
    """Load labels from a VTI file"""
    if not os.path.exists(vti_path):
        return None
    
    reader = vtk.vtkXMLImageDataReader()
    reader.SetFileName(vti_path)
    reader.Update()
    
    vti_img = reader.GetOutput()
    labels_array = vti_img.GetPointData().GetArray('labels')
    
    if labels_array is not None:
        dims = vti_img.GetDimensions()
        labels = vtk_to_numpy(labels_array).reshape(dims, order='F')
        labels = np.transpose(labels, (1, 2, 0))  # Reorder to (Y, X, Z)
        return labels
    return None

def addSkinFat_debug(a, random=False):
    """
    Debug version of addSkinFat function with detailed logging
    """
    print(f"Input array shape: {a.shape}")
    print(f"Input array unique labels: {sorted(np.unique(a))}")
    
    outside = a == 0
    body = a == 9
    
    print(f"Outside (label 0) voxels: {np.sum(outside)}")
    print(f"Body (label 9) voxels: {np.sum(body)}")
    
    if np.sum(body) == 0:
        print("❌ No body tissue (label 9) found! Cannot add skin/fat.")
        return a
    
    if random:
        fat_thickness = np.random.random()*25
    else:
        fat_thickness = 12
    
    print(f"Fat thickness: {fat_thickness}")
    
    # Distance transform
    print("Computing distance transform...")
    distance_from_outside = dtf(1-outside)
    print(f"Distance transform range: {distance_from_outside.min():.2f} to {distance_from_outside.max():.2f}")
    
    # Create fat region
    r = (distance_from_outside < fat_thickness) * (1-outside)
    print(f"Fat region (r) voxels: {np.sum(r)}")
    
    # Create mask for body tissue within fat region
    mask = r * body
    print(f"Body tissue within fat region: {np.sum(mask)}")
    
    if np.sum(mask) == 0:
        print("❌ No body tissue found within fat region! No skin/fat will be added.")
        return a
    
    # Apply skin/fat
    a_new = a * (1-mask) + mask * 99
    
    print(f"Output array unique labels: {sorted(np.unique(a_new))}")
    print(f"Skin/fat (label 99) voxels added: {np.sum(a_new == 99)}")
    
    return a_new

def test_addskinfat_on_real_data():
    """Test addSkinFat on real processed data"""
    test_file = "/home/<USER>/XCAT_Project/outputData/female_149/slice_566/Model.vti"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return
    
    print(f"🔍 Testing addSkinFat on: {test_file}")
    print("=" * 60)
    
    # Load the data
    labels = load_vti_labels(test_file)
    if labels is None:
        print("❌ Could not load labels")
        return
    
    print(f"Loaded labels shape: {labels.shape}")
    print(f"Original unique labels: {sorted(np.unique(labels))}")
    
    # Test on a single 2D slice (middle slice)
    mid_slice = labels.shape[2] // 2
    slice_2d = labels[:, :, mid_slice]
    
    print(f"\nTesting on 2D slice {mid_slice}:")
    print(f"Slice shape: {slice_2d.shape}")
    
    # Apply addSkinFat
    result_2d = addSkinFat_debug(slice_2d)
    
    # Test on full 3D volume
    print(f"\nTesting on full 3D volume:")
    result_3d = addSkinFat_debug(labels)
    
    return result_2d, result_3d

def test_addskinfat_synthetic():
    """Test addSkinFat on synthetic data to understand the logic"""
    print("\n🧪 Testing addSkinFat on synthetic data:")
    print("=" * 60)
    
    # Create a simple synthetic phantom
    size = 50
    phantom = np.zeros((size, size), dtype=int)
    
    # Create a circular body region (label 9)
    center = size // 2
    y, x = np.ogrid[:size, :size]
    mask = (x - center)**2 + (y - center)**2 <= (size//4)**2
    phantom[mask] = 9
    
    # Add some other tissue inside
    inner_mask = (x - center)**2 + (y - center)**2 <= (size//8)**2
    phantom[inner_mask] = 1  # Some other tissue
    
    print(f"Synthetic phantom shape: {phantom.shape}")
    print(f"Synthetic phantom labels: {sorted(np.unique(phantom))}")
    
    # Apply addSkinFat
    result = addSkinFat_debug(phantom)
    
    return phantom, result

def main():
    """Main debug function"""
    print("🔧 Debugging addSkinFat function")
    print("=" * 60)
    
    # Test 1: Synthetic data
    phantom, result_synthetic = test_addskinfat_synthetic()
    
    # Test 2: Real data
    try:
        result_2d, result_3d = test_addskinfat_on_real_data()
    except Exception as e:
        print(f"❌ Error testing real data: {e}")
        result_2d, result_3d = None, None
    
    # Summary
    print("\n📊 Summary:")
    print("=" * 60)
    
    if result_synthetic is not None:
        has_skinfat_synthetic = 99 in np.unique(result_synthetic)
        print(f"Synthetic data: {'✅ Skin/fat generated' if has_skinfat_synthetic else '❌ No skin/fat'}")
    
    if result_2d is not None:
        has_skinfat_2d = 99 in np.unique(result_2d)
        print(f"Real data (2D slice): {'✅ Skin/fat generated' if has_skinfat_2d else '❌ No skin/fat'}")
    
    if result_3d is not None:
        has_skinfat_3d = 99 in np.unique(result_3d)
        print(f"Real data (3D volume): {'✅ Skin/fat generated' if has_skinfat_3d else '❌ No skin/fat'}")

if __name__ == "__main__":
    main()
