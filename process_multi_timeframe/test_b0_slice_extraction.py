#!/usr/bin/env python3
"""
Test B0 slice extraction with dynamic slice ranges
"""

import os
import sys
import numpy as np

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_processor import load_and_crop_b0_fields
from t2star_updater import process_t2star_volume_3d

def test_b0_slice_extraction():
    """Test B0 slice extraction with different z_indices"""
    print("Testing B0 slice extraction with dynamic slice ranges")
    print("=" * 60)
    
    # Test different slice ranges
    test_cases = [
        {
            'name': 'Small range (slices 200-250)',
            'z_indices': list(range(200, 251)),
            'expected_start': 200,
            'expected_end': 250
        },
        {
            'name': 'Large range (slices 150-400)',
            'z_indices': list(range(150, 401)),
            'expected_start': 150,
            'expected_end': 400
        },
        {
            'name': 'Custom range (slices 100-200)',
            'z_indices': list(range(100, 201)),
            'expected_start': 100,
            'expected_end': 200
        },
        {
            'name': 'No z_indices (should use fallback)',
            'z_indices': None,
            'expected_start': 199,
            'expected_end': 306
        }
    ]
    
    # Create mock B0 field
    mock_b0_path = "/tmp/mock_b0_field.npy"
    mock_b0_field = np.random.uniform(-200, 200, (500, 500, 950)).astype(np.float32)
    np.save(mock_b0_path, mock_b0_field)
    print(f"Created mock B0 field: {mock_b0_field.shape}")
    
    # Test each case
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # Mock config for B0 path
            import config
            original_b0_path = getattr(config, 'b0_map_path', None)
            config.b0_map_path = mock_b0_path
            
            # Test load_and_crop_b0_fields
            print("Testing load_and_crop_b0_fields...")
            b0_cardiac, shimmed_b0_cardiac = load_and_crop_b0_fields(
                time_frame=1,
                crop_bounds=None,
                cardiac_mask=None,
                z_indices=test_case['z_indices']
            )
            
            if b0_cardiac is not None:
                expected_slices = test_case['expected_end'] - test_case['expected_start'] + 1
                actual_slices = b0_cardiac.shape[2]
                print(f"✅ B0 extraction successful")
                print(f"   Expected slices: {expected_slices}")
                print(f"   Actual slices: {actual_slices}")
                print(f"   B0 shape: {b0_cardiac.shape}")
                
                if actual_slices == expected_slices:
                    print("   ✅ Slice count matches expected")
                else:
                    print("   ❌ Slice count mismatch")
            else:
                print("❌ B0 extraction failed")
            
            # Test T2* processing
            if test_case['z_indices'] is not None and len(test_case['z_indices']) <= 100:  # Only test small ranges
                print("Testing T2* processing...")
                
                # Create mock T2* and T2 volumes
                num_slices = len(test_case['z_indices'])
                mock_t2s = np.random.uniform(10, 50, (500, 500, num_slices)).astype(np.float32)
                mock_t2 = np.random.uniform(20, 200, (500, 500, num_slices)).astype(np.float32)
                
                t2star_plus = process_t2star_volume_3d(
                    mock_t2s, mock_t2, mock_b0_path,
                    output_folder=None,
                    z_indices=test_case['z_indices'],
                    labels_volume=None
                )
                
                if t2star_plus is not None:
                    print(f"   ✅ T2* processing successful")
                    print(f"   T2*+ shape: {t2star_plus.shape}")
                    print(f"   T2*+ range: [{np.min(t2star_plus):.2f}, {np.max(t2star_plus):.2f}]")
                else:
                    print("   ❌ T2* processing failed")
            
            # Restore original config
            if original_b0_path:
                config.b0_map_path = original_b0_path
            
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
            import traceback
            traceback.print_exc()
    
    # Cleanup
    if os.path.exists(mock_b0_path):
        os.remove(mock_b0_path)
    
    print("\n" + "=" * 60)
    print("B0 slice extraction test completed")

def test_with_real_config():
    """Test with real configuration"""
    print("\nTesting with real configuration...")
    print("=" * 40)
    
    try:
        from config import b0_map_path
        
        if os.path.exists(b0_map_path):
            print(f"Found real B0 map: {b0_map_path}")
            
            # Load and check B0 field
            b0_field = np.load(b0_map_path)
            print(f"Real B0 field shape: {b0_field.shape}")
            
            # Test with a small slice range
            test_z_indices = list(range(200, 251))  # 51 slices
            print(f"Testing with z_indices: {test_z_indices[0]} to {test_z_indices[-1]} ({len(test_z_indices)} slices)")
            
            b0_cardiac, shimmed_b0_cardiac = load_and_crop_b0_fields(
                time_frame=1,
                crop_bounds=None,
                cardiac_mask=None,
                z_indices=test_z_indices
            )
            
            if b0_cardiac is not None:
                print(f"✅ Real B0 extraction successful")
                print(f"   Extracted B0 shape: {b0_cardiac.shape}")
                print(f"   Expected slices: {len(test_z_indices)}")
                print(f"   Actual slices: {b0_cardiac.shape[2]}")
                
                if shimmed_b0_cardiac is not None:
                    print(f"   Shimmed B0 shape: {shimmed_b0_cardiac.shape}")
                else:
                    print("   No shimmed B0 generated")
            else:
                print("❌ Real B0 extraction failed")
                
        else:
            print(f"❌ B0 map not found: {b0_map_path}")
            
    except Exception as e:
        print(f"❌ Real config test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("B0 Slice Extraction Test Suite")
    print("=" * 60)
    
    # Run tests
    test_b0_slice_extraction()
    test_with_real_config()
    
    print("\n🎉 All tests completed!")
