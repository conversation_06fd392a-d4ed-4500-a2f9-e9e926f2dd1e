#!/usr/bin/env python3
"""
Label cleaning functionality for XCAT processing pipeline
Handles cleaning of wrong LV/RV tissue labels
"""

import numpy as np
from scipy import ndimage
import matplotlib.pyplot as plt

def clean_rv_wall_mask(data):
    """
    Filter RV wall mask to keep connected components that are adjacent to RV blood
    Uses scipy for much faster processing
    
    Parameters:
    -----------
    data : ndarray
        3D array with tissue labels where 2=RV wall and 6=RV blood
    
    Returns:
    --------
    ndarray
        Binary mask of cleaned RV wall
    """
    # Create masks for RV wall and blood
    rv_wall = (data == 2)
    rv_blood = (data == 6)
    
    # Find connected components using scipy (MUCH faster than manual flood fill)
    wall_components, num_components = ndimage.label(rv_wall)
    
    print(f"Found {num_components} RV wall components")
    
    # Create cleaned mask
    cleaned_rv_wall = np.zeros_like(rv_wall)
    
    # Create a dilated blood mask to check adjacency more efficiently
    # This is faster than checking every voxel's neighbors
    dilated_blood = ndimage.binary_dilation(rv_blood)
    
    # Check each component for adjacency to blood
    for label in range(1, num_components + 1):
        component = (wall_components == label)
        
        # Check if this component overlaps with dilated blood
        # (meaning it's adjacent to blood)
        if np.any(component & dilated_blood):
            cleaned_rv_wall |= component
    
    return cleaned_rv_wall

def create_tissue_groups(data):
    """
    Create tissue group masks with cleaned RV wall
    
    Parameters:
    -----------
    data : ndarray
        3D array with tissue labels
        
    Returns:
    --------
    tuple
        (cardiac_mask, lung_mask, tissue_mask, air_mask)
    """
    # Initialize masks for each group
    cardiac_mask = np.zeros_like(data, dtype=bool)
    lung_mask = np.zeros_like(data, dtype=bool)
    tissue_mask = np.zeros_like(data, dtype=bool)
    air_mask = np.zeros_like(data, dtype=bool)
    
    # Clean RV wall mask
    cleaned_rv_wall = clean_rv_wall_mask(data)
    
    # Cardiac tissues (labels 1, 5, 6 and cleaned label 2)
    cardiac_labels = [1, 3, 4, 5, 6, 7, 8, 50] #include pericardiac region
    for label in cardiac_labels:
        cardiac_mask |= (data == label)
    # Add cleaned RV wall
    cardiac_mask |= cleaned_rv_wall

    # FIXED: Add removed parts of RV wall to tissue mask
    removed_rv_wall = (data == 2) & ~cleaned_rv_wall
    tissue_mask |= removed_rv_wall
    
    # Lung (label 15,16)
    lung_labels = [15, 16]
    for label in lung_labels:
        lung_mask |= (data == label)
    
    # All other labeled tissues
    all_labels = np.unique(data)
    other_tissue_labels = [label for label in all_labels 
                          if label not in cardiac_labels + [0, 2, 15, 16]]
    for label in other_tissue_labels:
        tissue_mask |= (data == label)
    
    # Air (unlabeled regions, label 0)
    air_mask = (data == 0)
    
    return cardiac_mask, lung_mask, tissue_mask, air_mask

def clean_labels_volume(labels_volume, body_label=9):
    """
    Clean the labels volume by removing wrong RV wall components
    
    Parameters:
    -----------
    labels_volume : ndarray
        3D array with tissue labels
    body_label : int
        Label to assign to removed RV wall voxels (default: 9)
        
    Returns:
    --------
    ndarray
        Cleaned labels volume
    """
    print("Cleaning RV wall labels...")
    
    # Create a copy of the original labels
    cleaned_labels = labels_volume.copy()
    
    # Get cleaned RV wall mask
    cleaned_rv_wall = clean_rv_wall_mask(labels_volume)
    
    # Identify voxels that were originally RV wall (label 2) but are removed in cleaning
    removed_rv_wall = (labels_volume == 2) & (~cleaned_rv_wall)
    
    # Update these voxels to body label
    cleaned_labels[removed_rv_wall] = body_label
    
    print(f"Reassigned {np.sum(removed_rv_wall)} RV wall voxels to body tissue (label {body_label})")
    
    return cleaned_labels

def visualize_label_cleaning(original_labels, cleaned_labels, slice_idx=None):
    """
    Visualize the effect of label cleaning
    
    Parameters:
    -----------
    original_labels : ndarray
        Original 3D labels volume
    cleaned_labels : ndarray
        Cleaned 3D labels volume
    slice_idx : int, optional
        Slice index to visualize (default: middle slice)
    """
    if slice_idx is None:
        slice_idx = original_labels.shape[2] // 2
    
    # Create tissue group masks for visualization
    cardiac_orig, lung_orig, tissue_orig, air_orig = create_tissue_groups(original_labels)
    cardiac_clean, lung_clean, tissue_clean, air_clean = create_tissue_groups(cleaned_labels)
    
    plt.figure(figsize=(15, 10))
    
    # Original cardiac mask
    plt.subplot(241)
    plt.imshow(cardiac_orig[:, :, slice_idx], cmap='Reds')
    plt.title('Original Cardiac Tissues')
    plt.axis('off')
    
    # Cleaned cardiac mask
    plt.subplot(242)
    plt.imshow(cardiac_clean[:, :, slice_idx], cmap='Reds')
    plt.title('Cleaned Cardiac Tissues')
    plt.axis('off')
    
    # Difference in cardiac mask
    plt.subplot(243)
    diff_cardiac = cardiac_orig[:, :, slice_idx].astype(int) - cardiac_clean[:, :, slice_idx].astype(int)
    plt.imshow(diff_cardiac, cmap='RdBu', vmin=-1, vmax=1)
    plt.title('Cardiac Difference\n(Red=Removed)')
    plt.axis('off')
    
    # Original RV wall
    plt.subplot(244)
    rv_wall_orig = (original_labels[:, :, slice_idx] == 2)
    plt.imshow(rv_wall_orig, cmap='Blues')
    plt.title('Original RV Wall')
    plt.axis('off')
    
    # Cleaned RV wall
    plt.subplot(245)
    rv_wall_clean = (cleaned_labels[:, :, slice_idx] == 2)
    plt.imshow(rv_wall_clean, cmap='Blues')
    plt.title('Cleaned RV Wall')
    plt.axis('off')
    
    # RV wall difference
    plt.subplot(246)
    diff_rv = rv_wall_orig.astype(int) - rv_wall_clean.astype(int)
    plt.imshow(diff_rv, cmap='RdBu', vmin=-1, vmax=1)
    plt.title('RV Wall Difference\n(Red=Removed)')
    plt.axis('off')
    
    # Original tissue mask
    plt.subplot(247)
    plt.imshow(tissue_orig[:, :, slice_idx], cmap='Greens')
    plt.title('Original Other Tissues')
    plt.axis('off')
    
    # Cleaned tissue mask
    plt.subplot(248)
    plt.imshow(tissue_clean[:, :, slice_idx], cmap='Greens')
    plt.title('Cleaned Other Tissues\n(+Removed RV)')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    print("\nLabel cleaning statistics:")
    print(f"Original cardiac voxels: {np.sum(cardiac_orig)}")
    print(f"Cleaned cardiac voxels: {np.sum(cardiac_clean)}")
    print(f"Removed cardiac voxels: {np.sum(cardiac_orig) - np.sum(cardiac_clean)}")
    print(f"Original RV wall voxels: {np.sum(original_labels == 2)}")
    print(f"Cleaned RV wall voxels: {np.sum(cleaned_labels == 2)}")
    print(f"Removed RV wall voxels: {np.sum(original_labels == 2) - np.sum(cleaned_labels == 2)}")

def apply_label_cleaning_to_volume(params, body_label=9, debug=False):
    """
    Apply label cleaning to a parameter volume dictionary
    
    Parameters:
    -----------
    params : dict
        Dictionary containing parameter volumes including 'Labels'
    body_label : int
        Label to assign to removed RV wall voxels
    debug : bool
        Whether to show visualization
        
    Returns:
    --------
    dict
        Updated parameter dictionary with cleaned labels
    """
    if 'Labels' not in params:
        print("Warning: No Labels volume found in parameters")
        return params
    
    print("Applying label cleaning to volume...")
    
    # Clean the labels
    original_labels = params['Labels']
    cleaned_labels = clean_labels_volume(original_labels, body_label)
    
    # Update the parameters dictionary
    updated_params = params.copy()
    updated_params['Labels'] = cleaned_labels
    
    # Visualize if requested
    if debug:
        visualize_label_cleaning(original_labels, cleaned_labels)
    
    return updated_params
