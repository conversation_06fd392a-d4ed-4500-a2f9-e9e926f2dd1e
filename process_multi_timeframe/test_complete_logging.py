#!/usr/bin/env python3
"""
Test complete logging functionality - verify all print statements are captured
"""

import os
import sys
import time

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from logger import init_logger, get_logger

def test_complete_logging():
    """Test that all output is captured in log files"""
    print("Testing complete logging functionality...")
    
    # Initialize logger
    logger = init_logger(log_dir="logs")
    
    print("=" * 60)
    print("TESTING ALL TYPES OF OUTPUT")
    print("=" * 60)
    
    # Test regular print statements
    print("1. Regular print statement")
    print("   This should appear in both console and complete log file")
    
    # Test logger messages
    logger.info("2. Logger info message")
    logger.warning("3. Logger warning message")
    logger.error("4. Logger error message")
    
    # Test multi-line print
    print("""5. Multi-line print statement:
   Line 1 of multi-line output
   Line 2 of multi-line output
   Line 3 of multi-line output""")
    
    # Test formatted output
    data = {
        'parameter': 'T2*',
        'shape': (150, 150, 150),
        'range': [1.0, 50.0]
    }
    print(f"6. Formatted output:")
    print(f"   Parameter: {data['parameter']}")
    print(f"   Shape: {data['shape']}")
    print(f"   Range: {data['range']}")
    
    # Test progress-like output
    print("7. Progress simulation:")
    for i in range(5):
        print(f"   Processing step {i+1}/5...")
        time.sleep(0.1)
    
    # Test error simulation (to stderr)
    print("8. Error simulation (stderr):", file=sys.stderr)
    print("   This error message should also be captured", file=sys.stderr)
    
    # Test mixed logger and print
    logger.info("9. Mixed output test - logger message")
    print("   Followed by print statement")
    logger.warning("   Followed by logger warning")
    print("   And another print statement")
    
    # Test special characters and formatting
    print("10. Special characters and formatting:")
    print("    ✅ Success symbol")
    print("    ❌ Error symbol")
    print("    🔄 Processing symbol")
    print("    🎉 Celebration symbol")
    
    # Simulate XCAT-like output
    print("\n" + "=" * 60)
    print("SIMULATED XCAT PROCESSING OUTPUT")
    print("=" * 60)
    
    print("Input file: /path/to/input.vti")
    print("Output folder: /path/to/output")
    print("✅ Input file found")
    
    logger.info("Starting cardiac processing...")
    print("Loading and preprocessing: /path/to/input.vti")
    print("Loaded VTI file: /path/to/input.vti")
    print("Image dimensions: (950, 500, 500)")
    
    print("Model info:")
    print("Origin: (0.0, 0.0, 0.0)")
    print("Dimensions: [500, 500, 950]")
    print("Spacing: [0.002, 0.002, 0.002]")
    
    logger.info("Label cleaning completed")
    print("Successfully loaded labels from file")
    print("Labels shape: (500, 500, 950)")
    print("Unique labels: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, ...]")
    
    print("Wall voxels: 145830")
    print("Blood voxels: 13277")
    print("Found 131 slices containing cardiac structures")
    print("Original z-range: 556 to 805 (131 slices)")
    print("Extended z-range: 506 to 855 (350 slices)")
    print("Added 219 slices")
    print("Processing z-range: 506 to 855 (350 slices)")
    
    logger.info("Processing completed successfully")
    
    # Get log summary
    log_summary = logger.get_log_summary()
    
    print("\n" + "=" * 60)
    print("LOG FILE VERIFICATION")
    print("=" * 60)
    
    print("Log files created:")
    print(f"  Main log: {log_summary['main_log']}")
    print(f"  Error log: {log_summary['error_log']}")
    print(f"  Complete log: {log_summary['complete_log']}")
    print(f"  Log directory: {log_summary['log_directory']}")
    
    # Verify log files exist and contain content
    verification_results = {}
    
    for log_type, log_path in [
        ('main', log_summary['main_log']),
        ('error', log_summary['error_log']),
        ('complete', log_summary['complete_log'])
    ]:
        if log_path and os.path.exists(log_path):
            with open(log_path, 'r') as f:
                content = f.read()
                verification_results[log_type] = {
                    'exists': True,
                    'size': len(content),
                    'lines': len(content.splitlines())
                }
                print(f"  ✅ {log_type.capitalize()} log: {verification_results[log_type]['lines']} lines, {verification_results[log_type]['size']} chars")
        else:
            verification_results[log_type] = {'exists': False}
            print(f"  ❌ {log_type.capitalize()} log: Not found")
    
    # Check if complete log contains all expected content
    if verification_results['complete']['exists']:
        complete_log_path = log_summary['complete_log']
        with open(complete_log_path, 'r') as f:
            complete_content = f.read()
        
        # Check for key phrases that should be in the complete log
        expected_phrases = [
            "Regular print statement",
            "Logger info message",
            "Multi-line print statement",
            "Formatted output",
            "Progress simulation",
            "SIMULATED XCAT PROCESSING OUTPUT",
            "Image dimensions: (950, 500, 500)",
            "Processing z-range: 506 to 855 (350 slices)"
        ]
        
        missing_phrases = []
        for phrase in expected_phrases:
            if phrase not in complete_content:
                missing_phrases.append(phrase)
        
        if not missing_phrases:
            print("  ✅ Complete log contains all expected content")
        else:
            print(f"  ❌ Complete log missing {len(missing_phrases)} expected phrases:")
            for phrase in missing_phrases:
                print(f"      - '{phrase}'")
    
    # Restore stdout to ensure clean exit
    logger.restore_stdout()
    
    print("\n🎉 Complete logging test finished!")
    print("Check the log files to verify all output was captured.")
    
    return verification_results

if __name__ == "__main__":
    print("Complete Logging Test")
    print("=" * 40)
    
    results = test_complete_logging()
    
    # Summary
    print(f"\nTest Results:")
    for log_type, result in results.items():
        if result['exists']:
            print(f"  {log_type}: ✅ ({result['lines']} lines)")
        else:
            print(f"  {log_type}: ❌ (not found)")
