#!/usr/bin/env python3
"""
Cardiac cropping and NPZ saving functions for XCAT processing pipeline
Handles cropping of cardiac region and saving final parameter maps
"""

import os
import numpy as np
import datetime
from scipy import ndimage
import matplotlib.pyplot as plt
from t2star_updater import process_t2star_volume_3d, load_t2star_volume_from_slices
from label_cleaner import apply_label_cleaning_to_volume

def crop_cardiac_region(params, cardiac_mask, crop_size=(150, 150, 150), debug=True):
    """Crop all parameter maps to focus on the cardiac region with improved LV centering"""
    # Find cardiac tissues in the Labels volume
    labels = params['Labels']
    print(f"Labels volume shape: {labels.shape}")
    
    # Create mask for cardiac structures (LV wall=1, RV wall=2, LV blood=5, RV blood=6)
    lv_wall_mask = (labels == 1)
    lv_blood_mask = (labels == 5)
    rv_wall_mask = (labels == 2)
    rv_blood_mask = (labels == 6)
    
    # Combined masks
    print(f"Size of cardiac_mask: {cardiac_mask.shape}")
    lv_mask = cardiac_mask
    
    # Check if masks contain any voxels
    print(f"LV wall voxels: {np.sum(lv_wall_mask)}")
    print(f"LV blood voxels: {np.sum(lv_blood_mask)}")
    print(f"Total cardiac voxels: {np.sum(cardiac_mask)}")
    
    # Find center with priority on LV wall
    if np.sum(lv_mask) > 20:
        center = ndimage.center_of_mass(lv_mask)
        center = np.round(center).astype(int)
        print(f"Found LV center at {center}")
    elif np.sum(cardiac_mask) > 20:
        center = ndimage.center_of_mass(cardiac_mask)
        center = np.round(center).astype(int)
        print(f"Found cardiac center at {center}")
    else:
        center = np.array(labels.shape) // 2
        print(f"No cardiac structures found. Using volume center {center}")
    
    # Visualize the detected center and masks if debugging
    if debug:
        # Create central orthogonal slices through the detected center
        x_slice, y_slice, z_slice = center
        
        plt.figure(figsize=(16, 12))
        
        # Axial view with center marked
        plt.subplot(231)
        plt.imshow(labels[:, :, z_slice])
        plt.plot(y_slice, x_slice, 'r+', markersize=12)  # Mark center
        plt.title(f"Labels - Axial (z={z_slice})")
        
        # Coronal view with center marked
        plt.subplot(232)
        plt.imshow(labels[:, y_slice, :])
        plt.plot(z_slice, x_slice, 'r+', markersize=12)  # Mark center
        plt.title(f"Labels - Coronal (y={y_slice})")
        
        # Sagittal view with center marked
        plt.subplot(233)
        plt.imshow(labels[x_slice, :, :])
        plt.plot(z_slice, y_slice, 'r+', markersize=12)  # Mark center
        plt.title(f"Labels - Sagittal (x={x_slice})")
        
        # Visualize masks
        plt.subplot(234)
        plt.imshow(lv_wall_mask[:, :, z_slice], cmap='Reds')
        plt.title("LV Wall Mask")
        
        plt.subplot(235)
        plt.imshow(lv_blood_mask[:, :, z_slice], cmap='Blues')
        plt.title("LV Blood Mask")
        
        plt.subplot(236)
        overlay = np.zeros((labels.shape[0], labels.shape[1], 3))
        overlay[:,:,0] = lv_wall_mask[:, :, z_slice]  # Red = LV wall
        overlay[:,:,2] = lv_blood_mask[:, :, z_slice]  # Blue = LV blood
        overlay[:,:,1] = rv_wall_mask[:, :, z_slice] | rv_blood_mask[:, :, z_slice]  # Green = RV
        plt.imshow(overlay)
        plt.plot(y_slice, x_slice, 'y+', markersize=12)  # Mark center
        plt.title("Combined Cardiac Masks")
        
        plt.tight_layout()
        plt.show()
    
    # Calculate crop boundaries
    half_size = np.array(crop_size) // 2
    min_bounds = center - half_size
    max_bounds = center + half_size
    
    # Store original bounds for debugging
    original_bounds = (min_bounds.copy(), max_bounds.copy())
    
    # Ensure bounds are within volume dimensions, maintaining the center as much as possible
    vol_shape = np.array(labels.shape)
    for i in range(3):
        # Check lower bound
        if min_bounds[i] < 0:
            # Shift both bounds while keeping crop size constant
            shift = -min_bounds[i]
            min_bounds[i] = 0
            max_bounds[i] += shift
            
            # Check if the shift pushed the upper bound past the edge
            if max_bounds[i] > vol_shape[i]:
                # If so, clamp and recenter as best as possible
                max_bounds[i] = vol_shape[i]
                # Recalculate min_bound to maintain crop size if possible
                desired_size = crop_size[i]
                min_bounds[i] = max(0, max_bounds[i] - desired_size)
        
        # Check upper bound
        elif max_bounds[i] > vol_shape[i]:
            # Shift both bounds while keeping crop size constant
            shift = max_bounds[i] - vol_shape[i]
            max_bounds[i] = vol_shape[i]
            min_bounds[i] = max(0, min_bounds[i] - shift)
    
    # Print actual crop dimensions for debugging
    if debug:
        actual_size = max_bounds - min_bounds
        print(f"Actual crop dimensions: {actual_size}")
        print(f"Original bounds: {original_bounds}")
        print(f"Adjusted bounds: {(min_bounds, max_bounds)}")
        
        # Check how far the center shifted
        original_center = (original_bounds[0] + original_bounds[1]) / 2
        adjusted_center = (min_bounds + max_bounds) / 2
        shift = adjusted_center - original_center
        print(f"Center shifted by: {shift} ({np.linalg.norm(shift):.1f} units)")
    
    # Crop all parameter maps
    cropped = {}
    for name, volume in params.items():
        x_min, y_min, z_min = min_bounds
        x_max, y_max, z_max = max_bounds
        
        # Crop and store
        cropped[name] = volume[x_min:x_max, y_min:y_max, z_min:z_max]
        print(f"Cropped {name}: {volume.shape} → {cropped[name].shape}")
    
    # Add crop information for visualization and future use
    cropped['_center'] = center
    cropped['_bounds'] = (min_bounds, max_bounds)
    cropped['_original_bounds'] = original_bounds
    
    # Visualize final crop if debugging
    if debug:
        plt.figure(figsize=(16, 5))
        
        # Show original with crop outline
        plt.subplot(131)
        plt.imshow(labels[:,:,z_slice])
        plt.plot(y_slice, x_slice, 'r+', markersize=12)  # Mark center
        
        # Add crop outline
        x_min, y_min, _ = min_bounds
        x_max, y_max, _ = max_bounds
        plt.plot([y_min, y_max, y_max, y_min, y_min], 
                [x_min, x_min, x_max, x_max, x_min], 'r-', linewidth=2)
        plt.title("Original with Crop Outline")
        
        # Show cropped volume - labels
        plt.subplot(132)
        plt.imshow(cropped['Labels'][:,:,cropped['Labels'].shape[2]//2])
        plt.title("Cropped Labels")
        
        # Show cropped volume - T2*
        plt.subplot(133)
        if 'T2*+' in cropped:
            plt.imshow(cropped['T2*+'][:,:,cropped['T2*+'].shape[2]//2], cmap='inferno')
            plt.colorbar(label='T2* (ms)')
            plt.title("Cropped T2*+")
        elif 'T2s' in cropped:
            plt.imshow(cropped['T2s'][:,:,cropped['T2s'].shape[2]//2], cmap='inferno')
            plt.colorbar(label='T2* (ms)')
            plt.title("Cropped T2*")
        
        plt.tight_layout()
        plt.show()
    
    return cropped

def crop_b0_field_aligned(b0_field, crop_bounds):
    """Crop B0 field using the same bounds as the parameter maps"""
    min_bounds, max_bounds = crop_bounds
    x_min, y_min, z_min = min_bounds
    x_max, y_max, z_max = max_bounds
    
    # Check bounds validity
    if (x_min >= 0 and y_min >= 0 and z_min >= 0 and 
        x_max <= b0_field.shape[0] and y_max <= b0_field.shape[1] and z_max <= b0_field.shape[2]):
        
        cropped_b0 = b0_field[x_min:x_max, y_min:y_max, z_min:z_max]
        print(f"B0 field cropped from {b0_field.shape} to {cropped_b0.shape}")
        return cropped_b0
    else:
        print(f"Error: Crop bounds {min_bounds}-{max_bounds} exceed B0 dimensions {b0_field.shape}")
        return None

def save_cardiac_npz(cardiac_params, b0_cardiac=None, shimmed_b0_cardiac=None, output_folder_bin=None, 
                     time_frame=None, subject_id=None):
    """
    Save cropped cardiac parameter maps to NPZ file
    
    Args:
        cardiac_params: Dictionary containing cropped cardiac parameters
        b0_cardiac: Cropped B0 field (optional)
        shimmed_b0_cardiac: Cropped shimmed B0 field (optional)
        output_folder_bin: Output folder path
        time_frame: Time frame number
        subject_id: Subject identifier
    """
    if output_folder_bin is None:
        output_folder_bin = "."
    
    # Define output directory
    output_dir = os.path.join(os.path.dirname(output_folder_bin), 'cardiac_region_new')
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")
    
    # Extract individual parameters
    PD_cardiac = cardiac_params.get('PD')
    T1_cardiac = cardiac_params.get('T1')
    T2_cardiac = cardiac_params.get('T2')
    T2star_cardiac = cardiac_params.get('T2s', cardiac_params.get('T2*'))
    T2star_plus_cardiac = cardiac_params.get('T2*+', T2star_cardiac)  # Use T2* if T2*+ not available
    Labels_cardiac = cardiac_params.get('Labels')
    
    # Create save dictionary with the exact format from the notebook
    save_dict = {
        'PD': PD_cardiac,
        'T1': T1_cardiac,
        'T2': T2_cardiac,
        'T2star': T2star_cardiac,
        'T2star_plus': T2star_plus_cardiac,
        'Labels': Labels_cardiac
    }
    
    # Add B0 fields if available
    if b0_cardiac is not None:
        save_dict['B0'] = b0_cardiac
    if shimmed_b0_cardiac is not None:
        save_dict['ShimmedB0'] = shimmed_b0_cardiac
    
    # Save as compressed NPZ file
    npz_filename = f"cardiac_volume.npz" if time_frame else "cardiac_volume.npz"
    cardiac_dict_path = os.path.join(output_dir, npz_filename)
    
    # Filter out None values
    filtered_save_dict = {k: v for k, v in save_dict.items() if v is not None}
    
    np.savez_compressed(cardiac_dict_path, **filtered_save_dict)
    print(f"Saved cardiac parameters to: {cardiac_dict_path}")
    
    # Save metadata
    metadata_filename = f"cardiac_metadata_frame_{time_frame:02d}.txt" if time_frame else "cardiac_metadata.txt"
    metadata_path = os.path.join(output_dir, metadata_filename)
    
    with open(metadata_path, 'w') as f:
        if 'Labels' in cardiac_params:
            f.write(f"Cropped volume shape: {cardiac_params['Labels'].shape}\n")
        if '_center' in cardiac_params:
            f.write(f"Cardiac center: {cardiac_params['_center']}\n")
        if '_bounds' in cardiac_params:
            f.write(f"Crop bounds: {cardiac_params['_bounds']}\n")
        if b0_cardiac is not None:
            f.write(f"B0 field shape: {b0_cardiac.shape}\n")
        if shimmed_b0_cardiac is not None:
            f.write(f"Shimmed B0 field shape: {shimmed_b0_cardiac.shape}\n")
        if time_frame is not None:
            f.write(f"Time frame: {time_frame}\n")
        if subject_id is not None:
            f.write(f"Subject ID: {subject_id}\n")
        f.write(f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"Saved metadata to: {metadata_path}")
    
    # Print summary
    print("\nSummary of saved cardiac parameter maps:")
    print(f"{'Parameter':<12} {'Shape':<15} {'Type':<12} {'Range':<20}")
    print("-" * 60)
    
    for name, data in filtered_save_dict.items():
        if data is not None:
            data_min, data_max = np.min(data), np.max(data)
            print(f"{name:<12} {str(data.shape):<15} {str(data.dtype):<12} [{data_min:.2f}, {data_max:.2f}]")
    
    return cardiac_dict_path, metadata_path
